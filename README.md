# cm-echo

## Що це таке?

Система для масового створення тікетів в CM (Case Management) з повним контролем процесу від початку до кінця.

Якщо ти коли-небудь намагався створити сотню тікетів вручну і втрачав лік десь на 37-му, то cm-echo — твій новий найкращий друг 🦸‍♂️

**cm-echo** дозволяє:

- ✅ Створювати тікети масово для багатьох абонентів
- ✅ Відстежувати прогрес в реальному часі
- ✅ Автоматично відновлювати процес після збоїв
- ✅ Зберігати історію всіх операцій

## 🧩 Архітектура

Система складається з 3 основних частин:

- **Бекенд**: Node.js + Express.js сервер з MariaDB для зберігання даних
- **Фронтенд**: Сучасний інтерфейс на Vue.js 3, Vite та TailwindCSS
- **Документація**: Автоматично генерована документація API за допомогою VitePress

## 🚀 Як почати?

### 🔧 Змінні середовища

Щоб cm-echo працював як швейцарський годинник, а не як будильник вашого сусіда о 5 ранку в неділю, налаштуйте ці змінні середовища у файлі `.env`:

```properties
# Підключення до MariaDB
MARIADB_HOST=хост бази даних MariaDB
MARIADB_PORT=порт бази даних MariaDB (за замовчуванням 3306)
MARIADB_USER=користувач MariaDB
MARIADB_PASSWORD=пароль MariaDB
MARIADB_NAME=назва бази даних MariaDB (за замовчуванням cm-echo)

# Налаштування сесій
SESSION_SECRET=секретний ключ для шифрування сесій
COOKIE_PATH=/cm-echo/

# API Layer
API_LAYER_AUTH=дані авторизації для API Layer у форматі Basic dGVzdDp0ZXN0IA

# Адміністрування
ADMIN_EMAIL=email адміна(або адмінів) для отримання запитів на реєстрацію

# Налаштування для розробки
NODE_ENV=production # тільки для продакшену
MODE=DEV # режим роботи (DEV або PROD)
PROJECT_NAME=cm-echo # назва проекту для логів
PROXY_URL=http://ЛОГІН:ПАРОЛЬ@proxy.example.com:3128/ # опціонально, для роботи через проксі

# Налаштування Vite (фронтенд)
VITE_DEV_DOCS_URL=URL для документації в режимі розробки
VITE_DEV_FRONTEND_URL=URL для фронтенду в режимі розробки
VITE_PROD_URL=URL для продакшен середовища
```

### 📦 Встановлення залежностей

Як і будь-який поважний розробник, наш проект має свої залежності. Але замість кави та енергетиків, йому потрібні npm пакети:

```bash
npm install --omit=dev
```

або якщо ви розробник

```bash
npm install
```

### 💻 Запуск

**Для звичайних смертних (запуск всієї системи):**

```bash
npm run build
```

```bash
export $(cat .env | grep -v '^#' | xargs) && npm start
```

**Для тих, хто любить бачити, як все горить в реальному часі:**

```bash
npm run dev
```

**Для розробників фронтенду:**

```bash
# Збірка фронтенду
npm run frontend:build

# Запуск фронтенду в режимі розробки
npm run frontend:dev
```

**Для розробників бекенду:**

```bash
# Запуск сервера
npm run server

# Запуск сервера в режимі розробки
npm run server:dev
```

**Для розробників документації:**

```bash
# Запуск документації в режимі розробки
npm run docs:dev

# Збірка документації
npm run docs:build

# Попередній перегляд зібраної документації
npm run docs:preview

# Очищення попередніх файлів документації
npm run docs:clean

# Повна перезбірка документації (очищення + примусова збірка)
npm run docs:rebuild
```

### 🔐 Управління користувачами

#### Створення тестового користувача

Потрібен доступ, але немає часу чекати на реєстрацію? Створіть тестового користувача за секунди:

```bash
npm run create-test-user
```

І отримайте суперкористувача з неймовірно захищеними обліковими даними:

- Логін: admin
- Пароль: password

(Жарт. В продакшені змініть ці дані, будь ласка!)

#### Створення нового користувача

Для створення нового користувача з випадковим паролем та відправкою даних на email:

```bash
npm run create-user
```

Слідуйте інструкціям у терміналі. Скрипт запитає email, створить користувача з логіном на основі email та відправить дані для входу на вказану адресу.

#### Скидання паролю користувача

Забули пароль? Не проблема! Скиньте його за допомогою команди:

```bash
npm run reset-password
```

Введіть email або логін користувача, і новий пароль буде згенеровано та відправлено на email.

### 🗄️ Створення бази даних

Немає бази даних? Не проблема! Одна команда і вона з'явиться, як за помахом чарівної палички:

```bash
npm run create-database
```

Магія? Ні, просто хороший код.

---

## 🌐 API: Ваш ключ до королівства

Більшість API методів захищені авторизацією. Виняток становлять методи автентифікації (`/cm-echo/api/auth/login`, `/cm-echo/api/auth/signup`, `/cm-echo/api/auth/request-reset`, `/cm-echo/api/auth/reset-password`) та `/cm-echo/version`. Для всіх інших методів без авторизації ви отримаєте лише 401 статус і нічого більше. Спочатку авторизуйтесь, потім керуйте світом тікетів.

### 🔑 Авторизація: Перепустка до функціоналу

#### 🎯 /cm-echo/api/auth/login

Метод для авторизації користувача.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| POST | Синхронна обробка |

##### 📜 Заголовки запиту

| Заголовок    | Значення           | Обов'язковий |
| ------------ | ------------------ | ------------ |
| Accept       | `*/*`              | так          |
| Content-type | `application/json` | так          |

##### 📩 Структура тіла запиту JSON

| Параметр | Тип    | Обов'язковий | Опис               |
| -------- | ------ | ------------ | ------------------ |
| username | string | **так**      | Ім'я користувача   |
| password | string | **так**      | Пароль користувача |

##### 🏹 Приклад запиту

```bash
curl -X POST http://localhost:8080/cm-echo/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}' \
  -c cookies.txt
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "message": "Authentication successful",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>"
  }
}
```

#### 🎯 /cm-echo/api/auth/me

Метод для отримання інформації про поточного користувача.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| GET  | Синхронна обробка |

##### 🏹 Приклад запиту

```bash
curl -X GET http://localhost:8080/cm-echo/api/auth/me \
  -b cookies.txt
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>"
  }
}
```

#### 🎯 /cm-echo/api/auth/logout

Метод для виходу з системи.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| POST | Синхронна обробка |

##### 🏹 Приклад запиту

```bash
curl -X POST http://localhost:8080/cm-echo/api/auth/logout \
  -b cookies.txt
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "message": "Logout successful"
}
```

#### 🎯 /cm-echo/api/auth/signup

Метод для відправки запиту на реєстрацію в системі. Надсилає email адміністратору.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| POST | Синхронна обробка |

##### 📜 Заголовки запиту

| Заголовок    | Значення           | Обов'язковий |
| ------------ | ------------------ | ------------ |
| Accept       | `*/*`              | так          |
| Content-type | `application/json` | так          |

##### 📩 Структура тіла запиту JSON

| Параметр | Тип    | Обов'язковий | Опис                              |
| -------- | ------ | ------------ | --------------------------------- |
| email    | string | **так**      | Email користувача                 |
| text     | string | **так**      | Повідомлення із запитом на доступ |

##### 🏹 Приклад запиту

```bash
curl -X POST http://localhost:8080/cm-echo/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","text":"Прошу надати доступ до системи"}'
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "message": "Запит успішно надіслано"
}
```

#### 🎯 /cm-echo/api/auth/change-password

Метод для зміни паролю авторизованого користувача.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| POST | Синхронна обробка |

##### 📜 Заголовки запиту

| Заголовок    | Значення           | Обов'язковий |
| ------------ | ------------------ | ------------ |
| Accept       | `*/*`              | так          |
| Content-type | `application/json` | так          |

##### 📩 Структура тіла запиту JSON

| Параметр        | Тип    | Обов'язковий | Опис            |
| --------------- | ------ | ------------ | --------------- |
| currentPassword | string | **так**      | Поточний пароль |
| newPassword     | string | **так**      | Новий пароль    |

##### 🏹 Приклад запиту

```bash
curl -X POST http://localhost:8080/cm-echo/api/auth/change-password \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{"currentPassword":"old_password","newPassword":"new_password"}'
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "message": "Пароль успішно змінено"
}
```

#### 🎯 /cm-echo/api/auth/request-reset

Метод для запиту на скидання паролю.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| POST | Синхронна обробка |

##### 📜 Заголовки запиту

| Заголовок    | Значення           | Обов'язковий |
| ------------ | ------------------ | ------------ |
| Accept       | `*/*`              | так          |
| Content-type | `application/json` | так          |

##### 📩 Структура тіла запиту JSON

| Параметр | Тип    | Обов'язковий | Опис               |
| -------- | ------ | ------------ | ------------------ |
| email    | string | **так**      | Email користувача  |

##### 🏹 Приклад запиту

```bash
curl -X POST http://localhost:8080/cm-echo/api/auth/request-reset \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "message": "Якщо обліковий запис з цією електронною адресою існує, інструкції щодо скидання паролю будуть надіслані на вказану адресу"
}
```

#### 🎯 /cm-echo/api/auth/reset-password

Метод для скидання паролю за допомогою токена.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| POST | Синхронна обробка |

##### 📜 Заголовки запиту

| Заголовок    | Значення           | Обов'язковий |
| ------------ | ------------------ | ------------ |
| Accept       | `*/*`              | так          |
| Content-type | `application/json` | так          |

##### 📩 Структура тіла запиту JSON

| Параметр    | Тип    | Обов'язковий | Опис                            |
| ----------- | ------ | ------------ | ------------------------------- |
| token       | string | **так**      | Токен для скидання паролю       |
| newPassword | string | **так**      | Новий пароль (мінімум 8 символів) |

##### 🏹 Приклад запиту

```bash
curl -X POST http://localhost:8080/cm-echo/api/auth/reset-password \
  -H "Content-Type: application/json" \
  -d '{"token":"reset_token_from_email","newPassword":"new_password"}'
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "message": "Пароль успішно скинуто. Тепер ви можете увійти з новим паролем."
}
```

#### 🎯 /cm-echo/api/users

Метод для отримання списку всіх користувачів системи.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| GET  | Синхронна обробка |

##### 🏹 Приклад запиту

```bash
curl -X GET http://localhost:8080/cm-echo/api/users \
  -b cookies.txt
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "users": [
    {
      "id": 1,
      "username": "admin"
    },
    {
      "id": 2,
      "username": "user1"
    }
  ]
}
```

#### 🎯 /cm-echo/api/auth/manage-lockout

Метод для блокування/розблокування облікових записів користувачів (лише для адміністраторів).

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| POST | Синхронна обробка |

##### 📜 Заголовки запиту

| Заголовок    | Значення           | Обов'язковий |
| ------------ | ------------------ | ------------ |
| Accept       | `*/*`              | так          |
| Content-type | `application/json` | так          |

##### 📩 Структура тіла запиту JSON

| Параметр | Тип     | Обов'язковий | Опис                                           |
| -------- | ------- | ------------ | ---------------------------------------------- |
| username | string  | **так**      | Ім'я користувача для блокування/розблокування  |
| isLocked | boolean | **так**      | true - заблокувати, false - розблокувати       |

##### 🏹 Приклад запиту

```bash
curl -X POST http://localhost:8080/cm-echo/api/auth/manage-lockout \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{"username":"user1","isLocked":true}'
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "message": "Обліковий запис користувача заблоковано"
}
```

### 🎫 Створення тікетів: Головна зірка шоу

#### 🎯 /cm-echo/api/create-tickets

Метод для створення тікетів в системі CM.

##### 📨 Метод запиту

| HTTP | Тип                |
| ---- | ------------------ |
| POST | Асинхронна обробка |

##### 📜 Заголовки запиту

| Заголовок    | Значення           | Обов'язковий |
| ------------ | ------------------ | ------------ |
| Accept       | `*/*`              | так          |
| Content-type | `application/json` | так          |

##### 📩 Структура тіла запиту JSON

| Параметр          | Тип    | Обов'язковий | Опис                                                                                                                                     |
| ----------------- | ------ | ------------ | ---------------------------------------------------------------------------------------------------------------------------------------- |
| MSISDN            | string | **так**      | Номери абонентів, розділені комами                                                                                                       |
| SubscriptionId    | string | ні           | Ідентифікатори підписок, розділені комами                                                                                                |
| CategoryName      | string | **так**      | Назва категорії тікета                                                                                                                   |
| VirtualTeamName   | string | **так**      | Назва віртуальної команди                                                                                                                |
| ReactionName      | string | **так**      | Назва реакції                                                                                                                            |
| ComplainDetails   | string | **так**      | Деталі скарги                                                                                                                            |
| Title             | string | **так**      | Заголовок тікета                                                                                                                         |
| ResolutionSummary | string | **так**      | Короткий опис рішення                                                                                                                    |
| TickerStatus      | string | **так**      | Статус тікета (1 - активний, 2 - закритий, 3 - відкладений, 4 - видалений, 5 - складений)                                                |
| NotificationType  | string | **так**      | Тип сповіщення (1 - Email, 2 - SMS, 3 - Немає, 4 - Зворотній дзвінок)                                                                    |
| InformSubscriber  | string | **так**      | Спосіб інформування абонента (1 - Телефонний дзвінок, 2 - За рішенням - SMS без подальших оновлень, 3 - SMS, 4 - Не потрібно, 5 - Email) |
| ResolutionReason  | string | **так**      | Причина рішення (4 - Проінформовано по телефону, 6 - Проінформовано через SMS, 19 - Інформування не потрібне)                            |

##### 🏹 Приклад запиту

```bash
curl -X POST http://localhost:8080/cm-echo/api/create-tickets \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{
    "MSISDN": "380501234567,380501234568",
    "SubscriptionId": "123456,123457",
    "CategoryName": "Скарга",
    "VirtualTeamName": "Відділ обслуговування",
    "ReactionName": "Стандартна",
    "ComplainDetails": "Деталі скарги",
    "Title": "Заголовок тікета",
    "ResolutionSummary": "Короткий опис рішення",
    "TicketStatus": "1",
    "NotificationType": "3",
    "InformSubscriber": "4",
    "ResolutionReason": "19"
  }'
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "processing_started",
  "sessionId": "1a2b3c4d5e6f7g8h9i0j",
  "message": "Processing started. Connect to the SSE endpoint to monitor progress.",
  "sseUrl": "/cm-echo/api/create-tickets/events/1a2b3c4d5e6f7g8h9i0j"
}
```

#### 🎯 /cm-echo/api/create-tickets/events/:sessionId

Метод для моніторингу процесу створення тікетів через Server-Sent Events (SSE).

##### 📨 Метод запиту

| HTTP | Тип |
| ---- | --- |
| GET  | SSE |

##### 🏹 Приклад запиту

```bash
curl -X GET http://localhost:8080/cm-echo/api/create-tickets/events/1a2b3c4d5e6f7g8h9i0j \
  -b cookies.txt
```

#### 🎯 /cm-echo/api/create-tickets/status/:sessionId

Метод для перевірки статусу процесу створення тікетів.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| GET  | Синхронна обробка |

##### 🏹 Приклад запиту

```bash
curl -X GET http://localhost:8080/cm-echo/api/create-tickets/status/1a2b3c4d5e6f7g8h9i0j \
  -b cookies.txt
```

##### ✅ Приклад успішної відповіді

```json
{
  "sessionId": "1a2b3c4d5e6f7g8h9i0j",
  "status": "processing",
  "total": 2,
  "processed": 1,
  "lastEvent": {
    "status": "processed",
    "total": 2,
    "processed": 1,
    "current": "380501234567",
    "message": "MSISDN 380501234567 processed (1/2)"
  }
}
```

#### 🎯 /cm-echo/api/create-tickets/:sessionId

Метод для скасування процесу створення тікетів.

##### 📨 Метод запиту

| HTTP   | Тип               |
| ------ | ----------------- |
| DELETE | Синхронна обробка |

##### 🏹 Приклад запиту

```bash
curl -X DELETE http://localhost:8080/cm-echo/api/create-tickets/1a2b3c4d5e6f7g8h9i0j \
  -b cookies.txt
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "cancelled",
  "message": "Processing cancelled successfully"
}
```

#### 🎯 /cm-echo/api/create-tickets/pause/:sessionId

Метод для призупинення активного процесу створення тікетів.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| POST | Синхронна обробка |

##### 🏹 Приклад запиту

```bash
curl -X POST http://localhost:8080/cm-echo/api/create-tickets/pause/1a2b3c4d5e6f7g8h9i0j \
  -b cookies.txt
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "paused",
  "message": "Processing paused successfully"
}
```

#### 🎯 /cm-echo/api/create-tickets/resume/:sessionId

Метод для відновлення призупиненого процесу створення тікетів.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| POST | Синхронна обробка |

##### 🏹 Приклад запиту

```bash
curl -X POST http://localhost:8080/cm-echo/api/create-tickets/resume/1a2b3c4d5e6f7g8h9i0j \
  -b cookies.txt
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "resumed",
  "message": "Processing resumed successfully"
}
```

### 📊 Управління сесіями: Огляд і контроль

#### 🎯 /cm-echo/api/sessions

Метод для отримання списку всіх сесій створення тікетів.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| GET  | Синхронна обробка |

##### 🏹 Приклад запиту

```bash
curl -X GET http://localhost:8080/cm-echo/api/sessions \
  -b cookies.txt
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "sessions": [
    {
      "id": "1a2b3c4d5e6f7g8h9i0j",
      "status": "completed",
      "total": 10,
      "processed": 10,
      "createdAt": "2025-05-01T12:00:00Z",
      "updatedAt": "2025-05-01T12:05:00Z"
    },
    {
      "id": "2b3c4d5e6f7g8h9i0j1k",
      "status": "processing",
      "total": 20,
      "processed": 15,
      "createdAt": "2025-05-02T10:00:00Z",
      "updatedAt": "2025-05-02T10:03:00Z"
    }
  ]
}
```

#### 🎯 /cm-echo/api/sessions/active

Метод для отримання списку активних сесій створення тікетів.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| GET  | Синхронна обробка |

##### 🏹 Приклад запиту

```bash
curl -X GET http://localhost:8080/cm-echo/api/sessions/active \
  -b cookies.txt
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "sessions": [
    {
      "id": "2b3c4d5e6f7g8h9i0j1k",
      "status": "processing",
      "total": 20,
      "processed": 15,
      "createdAt": "2025-05-02T10:00:00Z",
      "updatedAt": "2025-05-02T10:03:00Z"
    }
  ]
}
```

#### 🎯 /cm-echo/api/sessions/:sessionId

Метод для отримання детальної інформації про конкретну сесію.

##### 📨 Метод запиту

| HTTP | Тип               |
| ---- | ----------------- |
| GET  | Синхронна обробка |

##### 🏹 Приклад запиту

```bash
curl -X GET http://localhost:8080/cm-echo/api/sessions/1a2b3c4d5e6f7g8h9i0j \
  -b cookies.txt
```

##### ✅ Приклад успішної відповіді

```json
{
  "status": "success",
  "session": {
    "id": "1a2b3c4d5e6f7g8h9i0j",
    "status": "completed",
    "total": 10,
    "processed": 10,
    "createdAt": "2025-05-01T12:00:00Z",
    "updatedAt": "2025-05-01T12:05:00Z",
    "userId": 1,
    "parameters": {
      "MSISDN": "380501234567,380501234568",
      "CategoryName": "Скарга",
      "VirtualTeamName": "Відділ обслуговування"
    },
    "results": [
      {
        "msisdn": "380501234567",
        "status": "success",
        "ticketId": "TK123456",
        "timestamp": "2025-05-01T12:01:00Z"
      },
      {
        "msisdn": "380501234568",
        "status": "success",
        "ticketId": "TK123457",
        "timestamp": "2025-05-01T12:02:00Z"
      }
    ]
  }
}
```

---

## 🔍 Перевірка версії після деплою

```bash
curl -X GET \
  'http://localhost:8080/cm-echo/version' \
  --header 'Accept: */*' \
  --header 'Content-Type: application/json'
```

Версія повинна збігатися з тим, що записано в **[package.json](package.json)**.

Якщо версії не збігаються, у вас є кілька варіантів:

1. Ви деплоїли не ту версію
2. Хтось інший деплоїв не ту версію
3. Ви потрапили в паралельний всесвіт, де числа працюють інакше

Рекомендуємо розібратися з варіантами 1 і 2, перш ніж досліджувати варіант 3.
