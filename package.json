{"name": "cm-echo", "version": "1.0.0", "main": "src/server/index.js", "type": "module", "scripts": {"start": "npm run server", "dev": "concurrently \"npm run server:dev\" \"npm run frontend:dev\" \"npm run docs:dev\"", "build": "npm run frontend:build && npm run docs:build", "server": "node src/server/index.js", "server:dev": "nodemon --env-file=.env --watch src/server src/server/index.js", "frontend:dev": "cd src/frontend && vite", "frontend:build": "cd src/frontend && vue-tsc && vite build", "frontend:preview": "echo 'Frontend preview is now handled by the main server'", "docs:dev": "vitepress dev src/docs", "docs:build": "vitepress build src/docs", "docs:preview": "vitepress preview src/docs", "docs:clean": "node src/docs/clean-vitepress.js", "docs:rebuild": "npm run docs:clean && FORCE_DOCS_BUILD=true npm run docs:build", "create-database": "node --env-file=.env src/server/scripts/create-database.js", "create-test-user": "node --env-file=.env src/server/scripts/create-test-user.js", "create-user": "node --env-file=.env src/server/scripts/create-user.js", "reset-password": "node --env-file=.env src/server/scripts/reset-password.js"}, "keywords": [], "author": "kyivstar.tech", "license": "ISC", "description": "", "dependencies": {"@tanstack/vue-virtual": "^3.13.6", "@types/date-fns": "^2.5.3", "@types/lodash-es": "^4.17.12", "@vuepic/vue-datepicker": "^11.0.2", "@vueuse/core": "^10.11.1", "axios": "^1.6.7", "bcrypt": "^5.1.1", "cors": "^2.8.5", "date-fns": "^2.30.0", "express": "^5.1.0", "express-mysql-session": "^3.0.3", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "https-proxy-agent": "^7.0.6", "idb-keyval": "^6.2.1", "log4js": "^6.9.1", "mariadb": "^3.4.1", "mysql2": "^3.14.0", "node-fetch": "^3.3.2", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "pinia": "^2.1.7", "v-calendar": "^3.1.2", "vue": "^3.4.21", "vue-multiselect": "^3.2.0", "vue-router": "^4.3.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^20.11.30", "@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.19", "concurrently": "^8.2.2", "eslint": "^9.22.0", "globals": "^16.0.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "typescript": "^5.4.3", "vite": "6.3.4", "vitepress": "^1.6.3", "vue-tsc": "^2.0.6"}}