import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import { fileURLToPath, URL } from "node:url";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  const apiTarget =
    mode === "production" ? env.VITE_PROD_URL : env.VITE_DEV_DOCS_URL || "http://localhost:8080";

  return {
    plugins: [vue()],
    base: "/cm-echo/",
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes("node_modules")) {
              if (id.includes("vue") || id.includes("pinia")) {
                return "vendor";
              }
              return "dependencies";
            }
            if (id.includes("src/components")) {
              return "components";
            }
            if (id.includes("src/stores")) {
              return "stores";
            }
          },
        },
      },
    },
    server: {
      proxy: {
        "/cm-echo/api": {
          target: apiTarget,
          changeOrigin: true,
          rewrite: (path) => path,
        },
      },
    },
  };
});
