import { defineStore } from 'pinia';
import { ref } from 'vue';
import { authApi } from '../services/api';
import type { User } from '../types/user';
import { resetAuthCheck } from "../router";

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null);
  const isAuthenticated = ref(false);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  async function checkAuth() {
    isLoading.value = true;
    error.value = null;

    try {
      const userData = await authApi.checkAuth();
      user.value = userData;
      isAuthenticated.value = true;
    } catch (err) {
      user.value = null;
      isAuthenticated.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  async function login(username: string, password: string) {
    isLoading.value = true;
    error.value = null;

    try {
      const userData = await authApi.login(username, password);
      user.value = userData;
      isAuthenticated.value = true;
      return true;
    } catch (err: any) {
      error.value = err.message || "Помилка авторизації";
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  async function logout() {
    isLoading.value = true;

    try {
      await authApi.logout();
    } catch (err) {
      console.error("Logout error:", err);
    } finally {
      user.value = null;
      isAuthenticated.value = false;
      isLoading.value = false;

      localStorage.removeItem("currentSessionId");
      resetAuthCheck();
    }
  }

  async function requestSignup(email: string, text: string) {
    isLoading.value = true;
    error.value = null;

    try {
      const success = await authApi.requestSignup(email, text);
      return success;
    } catch (err: any) {
      error.value = err.message || "Помилка при відправці запиту";
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  async function changePassword(currentPassword: string, newPassword: string) {
    isLoading.value = true;
    error.value = null;

    try {
      const success = await authApi.changePassword(currentPassword, newPassword);
      return success;
    } catch (err: any) {
      error.value = err.message || "Помилка при зміні паролю";
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  async function requestPasswordReset(email: string) {
    isLoading.value = true;
    error.value = null;

    try {
      const success = await authApi.requestPasswordReset(email);
      return success;
    } catch (err: any) {
      error.value = err.message || "Помилка при запиті на скидання паролю";
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  async function resetPassword(token: string, newPassword: string) {
    isLoading.value = true;
    error.value = null;

    try {
      const success = await authApi.resetPassword(token, newPassword);
      return success;
    } catch (err: any) {
      error.value = err.message || "Помилка при скиданні паролю";
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    checkAuth,
    login,
    logout,
    requestSignup,
    changePassword,
    requestPasswordReset,
    resetPassword,
  };
});
