import { defineStore } from 'pinia';
import {
  isLoading,
  error,
  currentSession,
  eventSource,
  lastTotal,
  lastProcessed
} from './state';
import {
  createTickets,
  getSessionStatus,
  cancelTicketCreation,
  pauseTicketCreation,
  resumeTicketCreation,
  resetSession,
  checkForActiveSession,
  connectToEventStream,
  isSessionOwner
} from './actions';

export const useTicketsStore = defineStore("tickets", () => {
  return {
    isLoading,
    error,
    currentSession,
    createTickets,
    getSessionStatus,
    cancelTicketCreation,
    pauseTicketCreation,
    resumeTicketCreation,
    resetSession: () => resetSession({ value: eventSource.value }, currentSession, error, lastTotal, lastProcessed),
    checkForActiveSession,
    connectToEventStream,
    isSessionOwner,
  };
});
