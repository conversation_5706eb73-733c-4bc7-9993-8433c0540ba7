import { ticketsApi } from '../../../services/api';
import type { TicketFormData } from '../../../types/tickets';
import { isLoading, error, currentSession, lastTotal, lastProcessed } from '../state';
import { connectToEventStream } from '.';

export async function createTickets(formData: TicketFormData) {
  isLoading.value = true;
  error.value = null;

  try {
    const result = await ticketsApi.createTickets(formData);

    const initialTotal = result.total !== undefined ? result.total : 0;
    const initialProcessed = result.processed !== undefined ? result.processed : 0;

    lastTotal.value = initialTotal;
    lastProcessed.value = initialProcessed;

    currentSession.value = {
      sessionId: result.sessionId,
      status: "processing",
      total: initialTotal,
      processed: initialProcessed,
      events: [],
    };

    localStorage.setItem("currentSessionId", result.sessionId);

    connectToEventStream(result.sessionId);
    return true;
  } catch (err: any) {
    error.value = err.message || "Помилка при створенні тікетів";
    return false;
  } finally {
    isLoading.value = false;
  }
}
