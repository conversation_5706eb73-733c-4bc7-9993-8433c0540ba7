import { ticketsApi } from "../../../services/api";
import { isLoading, error, currentSession, eventSource, lastTotal, lastProcessed } from "../state";
import { connectToEventStream } from "./sessionManagement";

export async function cancelTicketCreation(sessionId: string) {
  isLoading.value = true;
  error.value = null;

  try {
    const success = await ticketsApi.cancelTicketCreation(sessionId);

    if (success) {
      if (currentSession.value) {
        currentSession.value.status = "cancelled";
      }

      if (eventSource.value) {
        eventSource.value.close();
        eventSource.value = null;
      }

      localStorage.removeItem("currentSessionId");

      return true;
    } else {
      error.value = "Помилка при скасуванні процесу";
      return false;
    }
  } catch (err: any) {
    error.value = err.message || "Помилка при скасуванні процесу";
    return false;
  } finally {
    isLoading.value = false;
  }
}

export async function pauseTicketCreation(sessionId: string) {
  error.value = null;

  try {
    if (currentSession.value && currentSession.value.sessionId === sessionId) {
    }

    if (currentSession.value && currentSession.value.sessionId === sessionId) {
      const updatedSession = { ...currentSession.value };
      updatedSession.status = "paused";
      updatedSession.paused = true;

      if (updatedSession.total === 0 && lastTotal.value > 0) {
        updatedSession.total = lastTotal.value;
      }
      if (updatedSession.processed === 0 && lastProcessed.value > 0) {
        updatedSession.processed = lastProcessed.value;
      }

      currentSession.value = updatedSession;
    }

    const success = await ticketsApi.pauseTicketCreation(sessionId);

    if (success) {
      if (currentSession.value && currentSession.value.sessionId === sessionId) {
        const updatedSession = { ...currentSession.value };
        updatedSession.status = "paused";
        updatedSession.paused = true;

        const pauseEvent = {
          status: "paused" as const,
          total: updatedSession.total,
          processed: updatedSession.processed,
          message: "Processing paused by user request",
          timestamp: new Date().toISOString(),
        };

        const eventExists = updatedSession.events.some(
          (e) => e.status === pauseEvent.status && e.message === pauseEvent.message
        );

        if (!eventExists) {
          updatedSession.events = [...updatedSession.events, pauseEvent];
        }

        currentSession.value = updatedSession;
      }

      if (eventSource.value) {
        eventSource.value.close();
        eventSource.value = null;
      }
      return true;
    } else {
      if (currentSession.value && currentSession.value.sessionId === sessionId) {
        const updatedSession = { ...currentSession.value };
        updatedSession.status = "processing";
        updatedSession.paused = false;
        currentSession.value = updatedSession;
      }
      error.value = "Помилка при призупиненні процесу";
      console.error(`Failed to pause session ${sessionId}`);
      return false;
    }
  } catch (err: any) {
    if (currentSession.value && currentSession.value.sessionId === sessionId) {
      const updatedSession = { ...currentSession.value };
      updatedSession.status = "processing";
      updatedSession.paused = false;
      currentSession.value = updatedSession;
    }
    error.value = err.message || "Помилка при призупиненні процесу";
    console.error(`Error pausing session ${sessionId}:`, err);
    return false;
  }
}

export async function resumeTicketCreation(sessionId: string) {
  error.value = null;

  try {
    if (currentSession.value && currentSession.value.sessionId === sessionId) {
    }

    if (currentSession.value && currentSession.value.sessionId === sessionId) {
      const updatedSession = { ...currentSession.value };
      updatedSession.status = "resumed";
      updatedSession.paused = false;

      if (updatedSession.total === 0 && lastTotal.value > 0) {
        updatedSession.total = lastTotal.value;
      }
      if (updatedSession.processed === 0 && lastProcessed.value > 0) {
        updatedSession.processed = lastProcessed.value;
      }

      currentSession.value = updatedSession;
    }

    const success = await ticketsApi.resumeTicketCreation(sessionId);

    if (success) {
      if (currentSession.value && currentSession.value.sessionId === sessionId) {
        const updatedSession = { ...currentSession.value };
        updatedSession.status = "resumed";
        updatedSession.paused = false;

        const resumeEvent = {
          status: "resumed" as const,
          total: updatedSession.total,
          processed: updatedSession.processed,
          message: "Processing resumed by user request",
          timestamp: new Date().toISOString(),
        };

        const eventExists = updatedSession.events.some(
          (e) => e.status === resumeEvent.status && e.message === resumeEvent.message
        );

        if (!eventExists) {
          updatedSession.events = [...updatedSession.events, resumeEvent];
        }

        currentSession.value = updatedSession;
      }

      connectToEventStream(sessionId);
      return true;
    } else {
      if (currentSession.value && currentSession.value.sessionId === sessionId) {
        const updatedSession = { ...currentSession.value };
        updatedSession.status = "paused";
        updatedSession.paused = true;
        currentSession.value = updatedSession;
      }
      error.value = "Помилка при відновленні процесу";
      console.error(`Failed to resume session ${sessionId}`);
      return false;
    }
  } catch (err: any) {
    if (currentSession.value && currentSession.value.sessionId === sessionId) {
      const updatedSession = { ...currentSession.value };
      updatedSession.status = "paused";
      updatedSession.paused = true;
      currentSession.value = updatedSession;
    }
    error.value = err.message || "Помилка при відновленні процесу";
    console.error(`Error resuming session ${sessionId}:`, err);
    return false;
  }
}
