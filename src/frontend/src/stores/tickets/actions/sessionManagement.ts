import { ticketsApi } from '../../../services/api';
import type { TicketEvent } from '../../../types/tickets';
import { isLoading, error, currentSession, eventSource, isProcessingEvent, lastTotal, lastProcessed } from '../state';
import { throttle, debounce } from '../helpers';
import { isSessionOwner, resetSession } from './sessionUtils';

export function connectToEventStream(sessionId: string) {
  if (eventSource.value) {
    eventSource.value.close();
  }

  if (!currentSession.value || currentSession.value.sessionId !== sessionId) {
    console.log(`No valid session found for ${sessionId}, fetching session data first`);
    getSessionStatus(sessionId)
      .then((session) => {
        if (session) {
          console.log(`Session data fetched for ${sessionId}, now connecting to event stream`);
          if (session.status === "resumed" || session.status === "processing") {
            session.paused = false;
            console.log(`Setting paused flag to false for resumed/processing session`);
          } else if (session.status === "paused") {
            session.paused = true;
            console.log(`Setting paused flag to true for paused session`);
          }
          currentSession.value = session;
          _createEventSourceConnection(sessionId);
        } else {
          console.error(`Failed to fetch session data for ${sessionId}`);
        }
      })
      .catch((err) => {
        console.error(`Error fetching session data for ${sessionId}:`, err);
      });
    return;
  }

  _createEventSourceConnection(sessionId);
}

function _createEventSourceConnection(sessionId: string) {
  console.log(`Creating new EventSource connection for session ${sessionId}`);

  getSessionStatus(sessionId)
    .then((session) => {
      if (session && currentSession.value) {
        const updatedSession = { ...currentSession.value };

        if (session.status === "resumed" || session.status === "processing") {
          updatedSession.paused = false;
          console.log(`Setting paused flag to false for resumed/processing session`);
        } else if (session.status === "paused") {
          updatedSession.paused = true;
          console.log(`Setting paused flag to true for paused session`);
        }

        if (session.events && session.events.length > 0) {
          console.log(`Updating events array with ${session.events.length} events from server`);
          updatedSession.events = session.events;
        }

        currentSession.value = updatedSession;
      }
    })
    .catch((err) => {
      console.error(`Error fetching latest session state: ${err}`);
    });

  const sse = new EventSource(`/cm-echo/api/create-tickets/events/${sessionId}`, {
    withCredentials: true,
  });

  sse.onopen = () => {
    console.log(`SSE connection opened for session ${sessionId}`);
  };

  sse.onerror = (err) => {
    console.error(`SSE connection error for session ${sessionId}:`, err);

    console.log("Browser will attempt to reconnect automatically...");

    setTimeout(async () => {
      if (sse.readyState === EventSource.CLOSED) {
        console.log("SSE connection still closed after timeout, checking session status");

        try {
          const session = await getSessionStatus(sessionId);

          if (session && !["completed", "failed", "cancelled"].includes(session.status)) {
            if (session.isOwner) {
              console.log("Session is valid and owned by current user, reconnecting");
              sse.close();
              eventSource.value = null;
              connectToEventStream(sessionId);
            } else {
              console.log("No permission to access this session");
              error.value = "У вас немає прав для доступу до цієї сесії";
              localStorage.removeItem("currentSessionId");
              resetSession(
                { value: eventSource.value },
                currentSession,
                error,
                lastTotal,
                lastProcessed
              );
            }
          } else if (session) {
            console.log(
              `Session ${sessionId} is in final state (${session.status}), not reconnecting`
            );
            localStorage.removeItem("currentSessionId");
          } else {
            console.log(`Session ${sessionId} not found, removing from localStorage`);
            localStorage.removeItem("currentSessionId");
            resetSession(
              { value: eventSource.value },
              currentSession,
              error,
              lastTotal,
              lastProcessed
            );
          }
        } catch (error) {
          console.error("Error checking session status:", error);
        }
      }
    }, 3000);
  };

  const updateProgressDebounced = debounce((total: number, processed: number) => {
    if (currentSession.value) {
      const updatedSession = { ...currentSession.value };

      if (total > 0 && (updatedSession.total === 0 || total >= updatedSession.total)) {
        updatedSession.total = total;
        lastTotal.value = total;
      } else if (updatedSession.total === 0 && lastTotal.value > 0) {
        updatedSession.total = lastTotal.value;
      }

      if (
        processed >= 0 &&
        (processed >= updatedSession.processed || updatedSession.processed === 0)
      ) {
        updatedSession.processed = processed;
        lastProcessed.value = processed;
      } else if (updatedSession.processed === 0 && lastProcessed.value > 0) {
        updatedSession.processed = lastProcessed.value;
      }

      currentSession.value = updatedSession;
    }
  }, 300);

  const throttledEventHandler = throttle((eventData: MessageEvent) => {
    if (isProcessingEvent.value) return;

    try {
      isProcessingEvent.value = true;
      const data = JSON.parse(eventData.data) as TicketEvent;

      console.log(`Received SSE event: ${data.status} - ${data.message}`);

      if (currentSession.value) {
        const updatedSession = { ...currentSession.value };

        // Distinguish between individual ticket errors and process-level status changes
        const isIndividualTicketError =
          data.status === "error" &&
          (data.message.includes("Error: CANT_PROCESS") ||
            data.message.includes("Error:") ||
            data.current); // Individual ticket errors usually have a 'current' field

        const isProcessLevelStatus =
          ["completed", "failed", "cancelled", "paused", "resumed"].includes(data.status) ||
          (data.status === "error" && !isIndividualTicketError);

        if (isProcessLevelStatus) {
          console.log(
            `Before update - Status: ${updatedSession.status}, paused flag: ${updatedSession.paused}, event status: ${data.status}`
          );

          updatedSession.status = data.status;

          if (data.status === "paused") {
            updatedSession.paused = true;
          } else if (data.status === "resumed" || data.status === "processing") {
            updatedSession.paused = false;
          }

          console.log(
            `Status updated to: ${data.status}, paused flag set to: ${updatedSession.paused}`
          );
        } else if (isIndividualTicketError) {
          // Individual ticket errors should not change the overall session status
          console.log(
            `Individual ticket error received: ${data.message} - not changing session status`
          );
          // Keep the session in processing state if it was processing
          if (updatedSession.status !== "paused" && updatedSession.status !== "resumed") {
            updatedSession.status = "processing";
            updatedSession.paused = false;
          }
        } else if (
          updatedSession.status !== "processing" &&
          updatedSession.status !== "paused" &&
          updatedSession.status !== "resumed"
        ) {
          updatedSession.status = "processing";
          updatedSession.paused = false;
          console.log(`Status set to processing, paused flag set to: false`);
        } else if (updatedSession.status === "resumed") {
          console.log(`Preserving resumed status in event handler`);
          updatedSession.paused = false;
        }

        console.log(
          `Current session status: ${updatedSession.status}, event status: ${data.status}, paused flag: ${updatedSession.paused}`
        );

        if (data.status === "processing" && updatedSession.status === "resumed") {
          console.log(
            `Received processing event after resume, ensuring paused flag is false but keeping status as resumed`
          );
          updatedSession.paused = false;
          data.status = "resumed";
        }

        if (data.status === "resumed") {
          console.log(
            `Received resumed event, ensuring paused flag is false and status is resumed`
          );
          updatedSession.paused = false;
          updatedSession.status = "resumed";
        }

        const hasProgressChanged =
          (data.total > 0 && data.total !== lastTotal.value) ||
          (data.processed >= 0 && data.processed !== lastProcessed.value);

        if (hasProgressChanged) {
          updateProgressDebounced(data.total, data.processed);
        }

        const eventExists = updatedSession.events.some(
          (e) =>
            e.status === data.status &&
            e.message === data.message &&
            e.processed === data.processed &&
            e.total === data.total
        );

        if (!eventExists) {
          console.log(
            `Adding new event: ${data.status} - ${data.message} (${data.processed}/${data.total})`
          );
          updatedSession.events = [...updatedSession.events, data];
        } else {
          console.log(
            `Event already exists, not adding: ${data.status} - ${data.message} (${data.processed}/${data.total})`
          );
        }

        console.debug(
          "Current events:",
          updatedSession.events.map((e) => `${e.status} - ${e.message} (${e.processed}/${e.total})`)
        );

        currentSession.value = updatedSession;

        // Only close the event stream for process-level final states, not individual ticket errors
        const shouldCloseStream =
          ["completed", "failed", "cancelled"].includes(data.status) ||
          (data.status === "error" && !isIndividualTicketError);

        if (shouldCloseStream) {
          console.log(`Received final status: ${data.status} with message: ${data.message}`);

          updatedSession.status = data.status;
          currentSession.value = updatedSession;

          console.log(
            `Final status details: status=${data.status}, message=${data.message}, total=${data.total}, processed=${data.processed}`
          );

          updateProgressDebounced.cancel?.();

          if (data.status === "completed") {
            console.log(
              `Processing completed with ${data.processed}/${data.total} items processed`
            );

            if (data.total > 0 && data.processed >= 0) {
              console.log(`Using event values: total=${data.total}, processed=${data.processed}`);
              lastTotal.value = data.total;
              lastProcessed.value = data.processed;

              const updatedSession = { ...currentSession.value };
              updatedSession.total = data.total;
              updatedSession.processed = data.processed;
              currentSession.value = updatedSession;
            } else if (data.total > 0 && lastProcessed.value > 0) {
              console.log(
                `Using mixed values: total=${data.total}, processed=${lastProcessed.value}`
              );
              lastTotal.value = data.total;

              const updatedSession = { ...currentSession.value };
              updatedSession.total = data.total;
              currentSession.value = updatedSession;
            } else if (data.processed >= 0 && lastTotal.value > 0) {
              console.log(
                `Using mixed values: total=${lastTotal.value}, processed=${data.processed}`
              );
              lastProcessed.value = data.processed;

              const updatedSession = { ...currentSession.value };
              updatedSession.processed = data.processed;
              currentSession.value = updatedSession;
            }

            if (data.message === "Process completed successfully") {
              console.log("Received final completion confirmation");

              if (lastTotal.value > 0) {
                lastProcessed.value = lastTotal.value;

                const updatedSession = { ...currentSession.value };
                updatedSession.processed = updatedSession.total;
                currentSession.value = updatedSession;

                console.log(`Forced progress to 100%: ${lastProcessed.value}/${lastTotal.value}`);
              }

              console.log(`Closing connection after final confirmation`);
              sse.close();
              eventSource.value = null;
            } else {
              console.log(`Waiting for final confirmation event...`);
            }
          } else {
            console.log(
              `Closing connection for ${data.status} status: ${data.processed}/${data.total}`
            );
            sse.close();
            eventSource.value = null;
          }
        } else if (isIndividualTicketError) {
          // Individual ticket errors should not close the stream - just log and continue
          console.log(`Individual ticket error logged, continuing processing: ${data.message}`);
        }
      }
      isProcessingEvent.value = false;
    } catch (err) {
      console.error("Error parsing SSE event:", err);
      isProcessingEvent.value = false;
    }
  }, 150);

  sse.onmessage = throttledEventHandler;

  eventSource.value = sse;
}

export async function getSessionStatus(sessionId: string) {
  isLoading.value = true;
  error.value = null;

  try {
    console.log(`Getting session status for ${sessionId}`);
    const session = await ticketsApi.getSessionById(sessionId);
    console.log(`Session data for ${sessionId}:`, JSON.stringify(session, null, 2));

    if (!session) {
      console.log(`Session ${sessionId} not found`);
      return null;
    }

    const currentSessionId = localStorage.getItem("currentSessionId");
    const isCurrentSession = currentSessionId === sessionId;

    const serverOwnership = session.isOwner === true;
    const localOwnership = isSessionOwner(session);
    const isOwned = serverOwnership || localOwnership;

    console.log(
      `Session ${sessionId} ownership check: server=${serverOwnership}, local=${localOwnership}, isOwned=${isOwned}`
    );

    if (!["completed", "failed", "cancelled"].includes(session.status) && isOwned) {
      console.log(`Session ${sessionId} is active and owned by current user`);

      if (isCurrentSession) {
        console.log(`Updating current session with ${sessionId}`);

        if (session.status === "resumed" || session.status === "processing") {
          session.paused = false;
          console.log(`Setting paused flag to false for resumed/processing session`);
        } else if (session.status === "paused") {
          session.paused = true;
          console.log(`Setting paused flag to true for paused session`);
        }

        lastTotal.value = session.total;
        lastProcessed.value = session.processed;
        currentSession.value = session;
      }

      localStorage.setItem("currentSessionId", sessionId);
    } else if (!["completed", "failed", "cancelled"].includes(session.status) && !isOwned) {
      console.log(`Session ${sessionId} is active but not owned by current user`);

      if (isCurrentSession) {
        console.log(`Removing non-owned session ${sessionId} from localStorage`);
        localStorage.removeItem("currentSessionId");
        resetSession({ value: eventSource.value }, currentSession, error, lastTotal, lastProcessed);
      }
    } else if (session) {
      console.log(`Session ${sessionId} is in final state (${session.status})`);

      if (isCurrentSession) {
        console.log(`Removing completed session ${sessionId} from localStorage`);
        localStorage.removeItem("currentSessionId");
      }
    }

    return session;
  } catch (err: any) {
    console.error(`Error getting session ${sessionId}:`, err);
    error.value = err.message || "Помилка при отриманні статусу сесії";

    return null;
  } finally {
    isLoading.value = false;
  }
}

export async function checkForActiveSession() {
  const savedSessionId = localStorage.getItem("currentSessionId");
  if (savedSessionId) {
    console.log(`Found saved session ID: ${savedSessionId}, attempting to reconnect`);
    try {
      const session = await getSessionStatus(savedSessionId);
      console.log("Session data received:", JSON.stringify(session, null, 2));

      if (!session) {
        console.log(`Session ${savedSessionId} not found, removing from localStorage`);
        localStorage.removeItem("currentSessionId");
      } else {
        const isFinalState = ["completed", "failed", "cancelled"].includes(session.status);
        if (isFinalState) {
          console.log(
            `Session ${savedSessionId} is in final state (${session.status}), removing from localStorage`
          );
          localStorage.removeItem("currentSessionId");
        } else {
          if (session.isOwner === true) {
            console.log(`Session ${savedSessionId} is marked as owned by server, reconnecting`);
            if (session.status === "resumed" || session.status === "processing") {
              session.paused = false;
              console.log(`Setting paused flag to false for resumed/processing session`);
            } else if (session.status === "paused") {
              session.paused = true;
              console.log(`Setting paused flag to true for paused session`);
            }

            lastTotal.value = session.total;
            lastProcessed.value = session.processed;
            currentSession.value = session;
            connectToEventStream(savedSessionId);
            return true;
          }

          const isLocallyOwned = isSessionOwner(session);
          if (isLocallyOwned) {
            console.log(
              `Session ${savedSessionId} is owned by current user (local check), reconnecting`
            );
            if (session.status === "resumed" || session.status === "processing") {
              session.paused = false;
              console.log(`Setting paused flag to false for resumed/processing session`);
            } else if (session.status === "paused") {
              session.paused = true;
              console.log(`Setting paused flag to true for paused session`);
            }

            lastTotal.value = session.total;
            lastProcessed.value = session.processed;
            currentSession.value = session;
            connectToEventStream(savedSessionId);
            return true;
          }

          console.log(
            `Session ${savedSessionId} is not owned by current user, removing from localStorage`
          );
          if (currentSession.value && currentSession.value.sessionId === savedSessionId) {
            resetSession(
              { value: eventSource.value },
              currentSession,
              error,
              lastTotal,
              lastProcessed
            );
          }
          localStorage.removeItem("currentSessionId");
        }
      }
    } catch (err) {
      console.error(`Error reconnecting to session ${savedSessionId}:`, err);
    }
  }

  try {
    console.log("Checking for active sessions from the server");
    const activeSessions = await ticketsApi.getActiveSessions();
    console.log("Active sessions from server:", activeSessions);

    if (activeSessions && activeSessions.length > 0) {
      const sortedSessions = [...activeSessions].sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
        const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
        return dateB.getTime() - dateA.getTime();
      });

      const mostRecentSession = sortedSessions[0];
      console.log(`Found active session ${mostRecentSession.sessionId} from server, reconnecting`);

      localStorage.setItem("currentSessionId", mostRecentSession.sessionId);
      lastTotal.value = mostRecentSession.total;
      lastProcessed.value = mostRecentSession.processed;
      currentSession.value = mostRecentSession;
      connectToEventStream(mostRecentSession.sessionId);
      return true;
    }
  } catch (err) {
    console.error("Error getting active sessions from server:", err);
  }

  return false;
}
