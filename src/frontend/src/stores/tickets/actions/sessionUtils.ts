import type { TicketSession } from '../../../types/tickets';
import { useAuthStore } from "../../auth";

export function isSessionOwner(session: TicketSession | null): boolean {
  if (!session) {
    console.log("isSessionOwner: session is null");
    return false;
  }

  const authStore = useAuthStore();

  if (!session.userId) {
    console.log("isSessionOwner: session has no userId, considering as owned");
    return true;
  }

  const isOwner = authStore.user?.id === session.userId;
  console.log(
    `isSessionOwner: comparing user IDs - current user: ${authStore.user?.id}, session user: ${session.userId}, isOwner: ${isOwner}`
  );
  return isOwner;
}

export function resetSession(
  eventSource: { value: EventSource | null },
  currentSession: { value: TicketSession | null },
  error: { value: string | null },
  lastTotal: { value: number },
  lastProcessed: { value: number }
) {
  if (eventSource.value) {
    eventSource.value.close();
    eventSource.value = null;
  }

  currentSession.value = null;
  error.value = null;
  lastTotal.value = 0;
  lastProcessed.value = 0;

  localStorage.removeItem("currentSessionId");
}
