import { ref, shallowRef } from "vue";
import type { TicketSession } from '../../types/tickets';

export const isLoading = ref(false);
export const error = ref<string | null>(null);
export const currentSession = shallowRef<TicketSession | null>(null);
export const eventSource = ref<EventSource | null>(null);
export const isProcessingEvent = ref(false);
export const lastTotal = ref(0);
export const lastProcessed = ref(0);
