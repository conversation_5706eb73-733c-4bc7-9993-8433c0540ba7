export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  let lastArgs: Parameters<T> | null = null;
  let lastExecution = 0;
  let timeoutId: ReturnType<typeof setTimeout> | null = null;

  return function (this: any, ...args: Parameters<T>): void {
    const now = Date.now();
    const remaining = limit - (now - lastExecution);

    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    if (remaining <= 0 || remaining > limit) {
      lastExecution = now;
      func.apply(this, args);
      inThrottle = true;
      lastArgs = null;

      timeoutId = setTimeout(() => {
        inThrottle = false;
        timeoutId = null;
        if (lastArgs) {
          const callFunc = func;
          const callArgs = lastArgs;
          lastArgs = null;
          callFunc.apply(this, callArgs);
          lastExecution = Date.now();
        }
      }, limit);
    } else if (!inThrottle) {
      inThrottle = true;
      lastArgs = args;

      timeoutId = setTimeout(() => {
        lastExecution = Date.now();
        inThrottle = false;
        timeoutId = null;
        if (lastArgs) {
          const callFunc = func;
          const callArgs = lastArgs;
          lastArgs = null;
          callFunc.apply(this, callArgs);
        }
      }, remaining);
    } else {
      lastArgs = args;
    }
  };
}
