import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { usePreferredDark } from '@vueuse/core'

type Theme = 'light' | 'dark' | 'system'

export const useThemeStore = defineStore('theme', () => {
  const theme = ref<Theme>('system')
  const prefersDark = usePreferredDark()
  
  const isDarkMode = ref(false);
  
  watch([theme, prefersDark], updateTheme, { immediate: true })
  
  function initTheme() {
    const savedTheme = localStorage.getItem("theme") as Theme | null;
    if (savedTheme) {
      theme.value = savedTheme;
    } else {
      theme.value = "system";
    }
    updateTheme();
  }
  
  function updateTheme() {
    if (theme.value === "system") {
      isDarkMode.value = prefersDark.value;
    } else {
      isDarkMode.value = theme.value === "dark";
    }

    if (isDarkMode.value) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }
  
  function setTheme(newTheme: Theme) {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
  }
  
  return {
    theme,
    isDarkMode,
    initTheme,
    setTheme
  }
})
