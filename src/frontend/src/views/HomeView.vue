<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="text-center">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white sm:text-4xl flex items-center justify-center">

        Вітаємо в CM Echo
      </h1>
      <p class="mt-3 max-w-2xl mx-auto text-xl text-gray-500 dark:text-gray-400 sm:mt-4">
        Система для масового створення тікетів у CM (Case Management) з повним контролем процесу від початку до кінця
      </p>
    </div>

    <div class="mt-12 grid gap-8 md:grid-cols-2 lg:grid-cols-3 auto-rows-fr">
      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg flex flex-col h-full">
        <div class="px-4 py-5 sm:p-6 flex-grow">
          <div class="flex">
            <div class="flex-shrink-0 self-start mt-1">
              <div class="bg-primary-100 dark:bg-primary-900 rounded-md p-3">
                <svg class="h-6 w-6 text-primary-600 dark:text-primary-400" xmlns="http://www.w3.org/2000/svg"
                  fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dt class="text-lg font-medium text-gray-900 dark:text-white">
                Масове створення тікетів
              </dt>
              <dd class="mt-2 text-base text-gray-500 dark:text-gray-400">
                Створюйте тікети для багатьох абонентів CM одночасно та відстежуйте процес у реальному часі
              </dd>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-4 sm:px-6 mt-auto">
          <div class="text-sm">
            <router-link to="/create-tickets"
              class="font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300">
              Перейти
              <span aria-hidden="true"> &rarr;</span>
            </router-link>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg flex flex-col h-full">
        <div class="px-4 py-5 sm:p-6 flex-grow">
          <div class="flex">
            <div class="flex-shrink-0 self-start mt-1">
              <div class="bg-primary-100 dark:bg-primary-900 rounded-md p-3">
                <svg class="h-6 w-6 text-primary-600 dark:text-primary-400" xmlns="http://www.w3.org/2000/svg"
                  fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dt class="text-lg font-medium text-gray-900 dark:text-white">
                Історія сесій
              </dt>
              <dd class="mt-2 text-base text-gray-500 dark:text-gray-400">
                Можна переглядати історію всіх попередніх процесів створення тікетів у зручному форматі
              </dd>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-4 sm:px-6 mt-auto">
          <div class="text-sm">
            <router-link to="/history"
              class="font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300">
              Перейти
              <span aria-hidden="true"> &rarr;</span>
            </router-link>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg flex flex-col h-full">
        <div class="px-4 py-5 sm:p-6 flex-grow">
          <div class="flex">
            <div class="flex-shrink-0 self-start mt-1">
              <div class="bg-primary-100 dark:bg-primary-900 rounded-md p-3">
                <svg class="h-6 w-6 text-primary-600 dark:text-primary-400" xmlns="http://www.w3.org/2000/svg"
                  fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dt class="text-lg font-medium text-gray-900 dark:text-white">
                Повний контроль
              </dt>
              <dd class="mt-2 text-base text-gray-500 dark:text-gray-400">
                Керуйте процесом створення тікетів. Можна призупинити або скасувати процес
              </dd>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-4 sm:px-6 mt-auto">
          <div class="text-sm">
            <router-link to="/create-tickets"
              class="font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300">
              Перейти
              <span aria-hidden="true"> &rarr;</span>
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-12 bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h2 class="text-lg font-medium text-gray-900 dark:text-white">Є питання?</h2>
        <div class="mt-4 text-gray-500 dark:text-gray-400">
          <p class="mb-4 ">
            <strong>CM Echo</strong> загалом має простий і доступний інтерфейс, але перед використанням треба обов'язково ознайомитися з <a href="https://cca.kyivstar.ua/cm-echo/docs/" target="_blank"
              rel="noopener noreferrer" class="btn-link">документацією <svg
                class="ml-0.4 h-4 w-4 inline-block align-[-3px]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                fill="currentColor">
                <path
                  d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
              </svg></a>
          </p>
          <p class="mb-4">
            Не любите багато читати? Чудово вас розумію! Але треба 😊 інакше є шанси створити багато тікетів із
            помилками. Якщо після прочитання документації у вас все ж залишаться питання, пишіть на <a class="btn-link"
              href="mailto:<EMAIL>">пошту</a>.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>