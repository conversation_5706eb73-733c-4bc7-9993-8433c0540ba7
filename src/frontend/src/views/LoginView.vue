<template>
  <div class="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2
          class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mr-2" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
            <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
            <path d="M9 14h6"></path>
            <path d="M9 10h6"></path>
            <path d="M12 18h.01"></path>
          </svg>
          CM Echo
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
          Система масового створення тікетів
        </p>
      </div>

      <div class="mt-8 bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <div v-if="showLoginForm">
          <form class="space-y-6" @submit.prevent="handleLogin">
            <div>
              <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Логін
              </label>
              <div class="mt-1">
                <input id="username" placeholder="oleksandr.panchenko" v-model="username" name="username" type="text"
                  required class="input w-full" />
              </div>
            </div>

            <div>
              <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Пароль
              </label>
              <div class="mt-1 relative">
                <input id="password" placeholder="qwerty123456" v-model="password" name="password"
                  :type="showPassword ? 'text' : 'password'" required class="input w-full pr-10" />
                <button type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
                  @click="showPassword = !showPassword">
                  <svg v-if="showPassword" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                    </path>
                  </svg>
                  <svg v-else class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21">
                    </path>
                  </svg>
                </button>
              </div>
            </div>

            <div v-if="authStore.error"
              class="p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-100 rounded-md flex items-start">
              <svg class="w-5 h-5 mr-2 flex-shrink-0 text-red-500 dark:text-red-300" fill="none" stroke="currentColor"
                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>{{ authStore.error }}</span>
            </div>

            <div>
              <button type="submit" class="btn btn-primary w-full flex justify-center" :disabled="authStore.isLoading">
                <span v-if="authStore.isLoading" class="mr-2">
                  <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                </span>
                Увійти
              </button>
            </div>

            <div class="mt-3 text-center">
              <router-link to="/forgot-password"
                class="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400"
                @click="authStore.error = null">
                Забули пароль?
              </router-link>
            </div>
          </form>

          <div class="mt-6">
            <div class="relative">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300 dark:border-gray-700"></div>
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
                  Або
                </span>
              </div>
            </div>

            <div class="mt-6">
              <button @click="authStore.error = null; showLoginForm = false"
                class="btn btn-secondary w-full flex justify-center">
                Запит на реєстрацію
              </button>
            </div>
          </div>
        </div>

        <div v-else>
          <form class="space-y-6" @submit.prevent="handleSignupRequest">
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email
              </label>
              <div class="mt-1">
                <input id="email" v-model="email" name="email" type="email" required class="input w-full" placeholder="<EMAIL>" />
              </div>
            </div>

            <div>
              <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Повідомлення
              </label>
              <div class="mt-1">
                <textarea id="message" v-model="message" name="message" rows="4" placeholder="Дайте доступ, ну будь ласка..." required
                  class="input w-full"></textarea>
              </div>
            </div>

            <div v-if="authStore.error"
              class="p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-100 rounded-md flex items-start">
              <svg class="w-5 h-5 mr-2 flex-shrink-0 text-red-500 dark:text-red-300" fill="none" stroke="currentColor"
                viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>{{ authStore.error }}</span>
            </div>

            <div v-if="signupSuccess"
              class="p-3 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-100 rounded-md flex items-start">
              <svg class="w-5 h-5 mr-2 flex-shrink-0 text-green-500 dark:text-green-300" fill="none"
                stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>Запит успішно відправлено. Адміністратор зв'яжеться з вами.</span>
            </div>

            <div>
              <button type="submit" class="btn btn-primary w-full flex justify-center" :disabled="authStore.isLoading">
                <span v-if="authStore.isLoading" class="mr-2">
                  <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                </span>
                Відправити запит
              </button>
            </div>
          </form>

          <div class="mt-6">
            <button @click="showLoginForm = true" class="btn btn-secondary w-full flex justify-center">
              Повернутися до входу
            </button>
          </div>
        </div>
      </div>

      <div class="mt-4 flex justify-center">
        <ThemeSwitcher />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import ThemeSwitcher from '../components/ui/ThemeSwitcher.vue';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

const showLoginForm = ref(true);
const username = ref('');
const password = ref('');
const email = ref('');
const message = ref('');
const signupSuccess = ref(false);
const showPassword = ref(false);

async function handleLogin() {
  const success = await authStore.login(username.value, password.value);

  if (success) {
    const redirectPath = route.query.redirect as string || '/';
    router.push(redirectPath);
  }
}

async function handleSignupRequest() {
  const success = await authStore.requestSignup(email.value, message.value);

  if (success) {
    signupSuccess.value = true;
    email.value = '';
    message.value = '';

    setTimeout(() => {
      signupSuccess.value = false;
      showLoginForm.value = true;
    }, 5000);
  }
}
</script>
