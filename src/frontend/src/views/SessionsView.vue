<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="mt-1">
      <div class="flex justify-between items-center mb-4">
        <p class="text-gray-500 dark:text-gray-400 flex items-center">
          Останні сесії створення тікетів
          <svg v-if="isLoading && !initialLoading" class="animate-spin ml-2 h-4 w-4 text-gray-500 dark:text-gray-400"
            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
          </svg>
        </p>

        <SessionFilters :isLoading="isLoading" @filter="handleFilterChange" @refresh="refreshData" />
      </div>

      <SessionsList :sessions="displayedSessions" :isLoading="isLoading" :initialLoading="initialLoading" :error="error"
        :hasMoreToLoad="hasMoreToLoad" @view-details="viewSessionDetails" @refresh="refreshData"
        @load-more="loadMore" />

      <SessionDetailsDialog v-if="selectedSession" :show="!!selectedSession" :session="selectedSession"
        :showManageButton="true" @close="closeSessionDetails" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onActivated, computed } from 'vue';
import { ticketsApi } from '../services/api';
import type { TicketSession } from '../types/tickets';
import SessionDetailsDialog from '../components/dialogs/SessionDetailsDialog.vue';
import { useCachedData } from '../composables/useCachedData';
import { CACHE_KEYS } from '../services/cacheService';
import { useSessionFilters } from '../composables/useSessionFilters';
import SessionFilters from '../components/sessions/SessionFilters.vue';
import SessionsList from '../components/sessions/SessionsList.vue';

const ITEMS_PER_PAGE = 8;

const {
  data: allSessions,
  isLoading,
  error,
  loadData: loadSessions,
  refreshData: refreshSessions
} = useCachedData<TicketSession[]>({
  cacheKey: CACHE_KEYS.SESSIONS,
  fetchFunction: ticketsApi.getSessions,
  showUpdateNotification: false
});

const { filterSessions, setFilters } = useSessionFilters();
const initialLoading = ref(true);
const selectedSession = ref<TicketSession | null>(null);
const filteredSessions = ref<TicketSession[]>([]);
const displayedSessions = computed(() => {
  if (filteredSessions.value.length <= ITEMS_PER_PAGE) {
    return filteredSessions.value;
  }
  return filteredSessions.value.slice(0, currentlyDisplayed.value);
});
const currentlyDisplayed = ref(ITEMS_PER_PAGE);
const hasMoreToLoad = computed(() => currentlyDisplayed.value < filteredSessions.value.length);

const handleFilterChange = (userId: number, status: string, dateRange?: [Date | null, Date | null]) => {
  setFilters(userId, status, dateRange);
  updateFilteredSessions();
  currentlyDisplayed.value = ITEMS_PER_PAGE;
};

const updateFilteredSessions = () => {
  filteredSessions.value = filterSessions(allSessions.value);
};

const refreshData = async () => {
  await refreshSessions();
  updateFilteredSessions();
  
  if (selectedSession.value) {
    try {
      const updatedSession = await ticketsApi.getSessionById(selectedSession.value.sessionId);
      selectedSession.value = updatedSession;
    } catch (err) {
      console.error('Error updating selected session details:', err);
    }
  }
};

const loadMore = () => {
  if (hasMoreToLoad.value) {
    currentlyDisplayed.value += ITEMS_PER_PAGE;
  }
};

function viewSessionDetails(sessionId: string) {
  const sessionFromList = filteredSessions.value.find(s => s.sessionId === sessionId);

  if (sessionFromList) {
    selectedSession.value = { ...sessionFromList };

    setTimeout(async () => {
      try {
        const session = await ticketsApi.getSessionById(sessionId);
        if (selectedSession.value && selectedSession.value.sessionId === sessionId) {
          selectedSession.value = session;
        }
      } catch (err) {
        console.error('Error fetching session details:', err);
      }
    }, 100);
  } else {
    selectedSession.value = {
      sessionId,
      status: 'processing',
      total: 0,
      processed: 0,
      events: []
    };

    setTimeout(async () => {
      try {
        console.log('Fetching session details for:', sessionId);
        const session = await ticketsApi.getSessionById(sessionId);
        if (selectedSession.value && selectedSession.value.sessionId === sessionId) {
          console.log('Updating placeholder with actual session data');
          selectedSession.value = session;
        }
      } catch (err) {
        console.error('Error fetching session details:', err);
        if (selectedSession.value && selectedSession.value.sessionId === sessionId) {
          error.value = 'Помилка завантаження деталей сесії';
          selectedSession.value = null;
        }
      }
    }, 100);
  }
}

function closeSessionDetails() {
  selectedSession.value = null;
}

onMounted(async () => {
  try {
    await loadSessions();
    initialLoading.value = false;
    updateFilteredSessions();
  } catch (err) {
    console.error('Error during component initialization:', err);
    if (!error.value) {
      error.value = 'Помилка ініціалізації компонента';
    }
  }
});

onActivated(async () => {
  console.log('SessionsView activated, updating data from cache and refreshing from server');
  updateFilteredSessions();
  try {
    await refreshData();
    console.log('SessionsView data refreshed from server');
  } catch (err) {
    console.error('Error refreshing data on activation:', err);
  }
});
</script>
