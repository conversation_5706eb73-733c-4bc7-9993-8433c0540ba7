<template>
  <div class="flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
      <div class="text-center">
        <h1 class="text-3xl font-extrabold text-gray-900 dark:text-white">CM Echo</h1>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Система масового створення тікетів</p>
      </div>

      <ResetPasswordForm />

      <div class="mt-4 flex justify-center">
        <ThemeSwitcher />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ResetPasswordForm from '../components/auth/ResetPasswordForm.vue';
import ThemeSwitcher from '../components/ui/ThemeSwitcher.vue';
</script>
