<template>
  <div class="documentation-container">
    <iframe
      ref="docsIframe"
      :src="docsUrl"
      class="docs-iframe"
      :class="{ 'loading': isLoading }"
      @load="handleIframeLoad"
    ></iframe>
    <div v-if="isLoading" class="loading-overlay">
      <div class="spinner"></div>
      <p class="loading-text">Завантаження документації...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useThemeStore } from '../stores/theme';

const themeStore = useThemeStore();
const docsIframe = ref<HTMLIFrameElement | null>(null);
const isLoading = ref(true);

const baseDocsUrl = '/cm-echo/docs/';

const docsUrl = computed(() => {
  return `${baseDocsUrl}`;
});

const handleIframeLoad = () => {
  isLoading.value = false;

  if (docsIframe.value && docsIframe.value.contentWindow) {
    try {
      docsIframe.value.contentWindow.postMessage({
        type: 'theme-change',
        theme: themeStore.theme
      }, '*');
    } catch (error) {
      console.error('Error sending theme to iframe:', error);
    }
  }
};

onMounted(() => {
  const sendThemeToIframe = () => {
    if (docsIframe.value && docsIframe.value.contentWindow) {
      try {
        docsIframe.value.contentWindow.postMessage({
          type: 'theme-change',
          theme: themeStore.theme
        }, '*');
      } catch (error) {
        console.error('Error sending theme to iframe:', error);
      }
    }
  };

  themeStore.$subscribe(() => {
    sendThemeToIframe();
  });
});
</script>

<style scoped>
.documentation-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 64px);
  overflow: hidden;
}

.docs-iframe {
  width: 100%;
  height: 100%;
  border: none;
  transition: opacity 0.3s ease;
}

.docs-iframe.loading {
  opacity: 0;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--color-background);
  z-index: 10;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

.loading-text {
  margin-top: 1rem;
  font-size: 1.125rem;
  color: var(--color-text);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

:deep(.dark) .spinner {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: var(--color-primary);
}
</style>
