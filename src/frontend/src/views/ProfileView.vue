<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="mt-1 grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="space-y-8 md:col-span-1">

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div class="px-4 py-5 sm:px-6 flex flex-col items-center">
            <div
              class="w-24 h-24 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center">
              <span class="text-3xl font-bold text-white">
                {{ user?.username ? user.username.charAt(0).toUpperCase() : '?' }}
              </span>
            </div>
            <!-- <h2 class="text-xl font-bold text-gray-800 dark:text-white">{{ user?.username }}</h2> -->
          </div>

          <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
            <div class="space-y-4">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Ім'я користувача</p>
                  <p class="text-sm text-gray-900 dark:text-white">{{ user?.username }}</p>
                </div>
              </div>

              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                  </path>
                </svg>
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
                  <p class="text-sm text-gray-900 dark:text-white">{{ user?.email || 'Не вказано' }}</p>
                </div>
              </div>

              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Дата реєстрації</p>
                  <p class="text-sm text-gray-900 dark:text-white">{{ user?.createdAt ? formatDateLong(user.createdAt) :
                    'Не вказано' }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                </path>
              </svg>
              Безпека
            </h3>
          </div>
          <div class="px-4 py-5 sm:px-6">
            <ChangePasswordForm class="max-w-none" />
          </div>
        </div>
      </div>

      <div class="md:col-span-2 flex flex-col">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden flex-1 flex flex-col">
          <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                  </path>
                </svg>
                Активність
                <svg v-if="isLoadingSessions && !initialLoading"
                  class="animate-spin ml-2 h-4 w-4 text-gray-800 dark:text-gray-200" xmlns="http://www.w3.org/2000/svg"
                  fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
              </h3>
              <button @click="refreshSessions"
                class="btn btn-secondary text-sm w-8 h-8 p-0 flex items-center justify-center" title="Оновити дані"
                :disabled="isLoadingSessions">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            </div>
          </div>

          <div class="px-4 py-5 sm:px-6 flex-1 flex flex-col">
            <div v-if="isLoadingSessions && (!sessions || !sessions.length) && initialLoading" class="flex-1">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 animate-pulse">
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                  <div class="h-8 bg-gray-200 dark:bg-gray-600 rounded mx-auto w-16 mb-1"></div>
                  <div class="h-4 bg-gray-200 dark:bg-gray-600 rounded mx-auto w-12"></div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                  <div class="h-8 bg-gray-200 dark:bg-gray-600 rounded mx-auto w-16 mb-1"></div>
                  <div class="h-4 bg-gray-200 dark:bg-gray-600 rounded mx-auto w-12"></div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                  <div class="h-8 bg-gray-200 dark:bg-gray-600 rounded mx-auto w-16 mb-1"></div>
                  <div class="h-4 bg-gray-200 dark:bg-gray-600 rounded mx-auto w-12"></div>
                </div>
              </div>

              <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">
                Останні сесії
              </h3>
              <ul class="divide-y divide-gray-200 dark:divide-gray-700 flex-1">
                <li v-for="i in 3" :key="i"
                  class="px-4 py-4 sm:px-6 flex justify-between items-center relative session-item animate-pulse">
                  <div class="flex items-center">
                    <div>
                      <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-40 mb-2"></div>
                      <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-2"></div>
                      <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
                    </div>
                  </div>
                  <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded-full w-20"></div>
                </li>
              </ul>
            </div>

            <div v-else-if="!sessions || sessions.length === 0"
              class="text-center py-16 flex-1 flex flex-col justify-center">
              <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                </path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Немає активності</h3>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Ваша активність буде відображатися тут після створення тікетів.
              </p>
              <div class="mt-6">
                <router-link to="/create-tickets"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Створити тікети
                </router-link>
              </div>
            </div>

            <div v-else class="flex-1 flex flex-col">

              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div
                  class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center cursor-default shadow-md dark:shadow-gray-900">
                  <p class="text-2xl font-bold text-primary-600 dark:text-primary-400">{{ sessions ? sessions.length : 0
                  }}</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Сесій</p>
                </div>
                <div
                  class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center cursor-default shadow-md dark:shadow-gray-900">
                  <p class="text-2xl font-bold text-primary-600 dark:text-primary-400">{{ completedSessions }}</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Завершено</p>
                </div>
                <div
                  class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center cursor-default shadow-md dark:shadow-gray-900">
                  <p class="text-2xl font-bold text-primary-600 dark:text-primary-400">{{ totalTickets }}</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Тікетів</p>
                </div>
              </div>

              <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">
                Останні сесії
              </h3>
              <ul class="divide-y divide-gray-200 dark:divide-gray-700 flex-1">
                <li v-for="session in recentSessions" :key="session.sessionId"
                  class="px-4 py-4 sm:px-6 flex justify-between items-center hover:bg-gray-50/70 dark:hover:bg-gray-700/70 transition-colors relative session-item">
                  <div class="flex items-center">
                    <div>
                      <p class="text-sm font-medium text-primary-600 dark:text-primary-400 truncate cursor-pointer hover:underline"
                        @click="viewSessionDetails(session.sessionId)">
                        {{ session.sessionId }}
                      </p>
                      <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        {{ formatDateToLocaleString(session.createdAt) }}
                      </p>
                      <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Прогрес: {{ session.processed }} / {{ session.total }} тікетів
                      </p>
                    </div>
                  </div>
                  <span class="status-badge" :class="{
                      'status-badge-created': session.status === 'created' || session.status === 'started' || session.status === 'resumed',
                      'status-badge-processing': session.status === 'processing',
                      'status-badge-waiting': session.status === 'waiting',
                      'status-badge-processed': session.status === 'processed',
                      'status-badge-completed': session.status === 'completed',
                      'status-badge-failed': session.status === 'failed' || session.status === 'error' || session.status === 'cancelled',
                      'status-badge-paused': session.status === 'paused'
                    }">
                    {{ getStatusText(session.status) }}
                  </span>
                </li>
              </ul>

              <div class="mt-0 text-center">
                <router-link to="/history"
                  class="text-sm font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">
                  Переглянути всю історію сесій →
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <SessionDetailsDialog v-if="selectedSession" :show="!!selectedSession" :session="selectedSession"
      :showManageButton="true" @close="closeSessionDetails" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import { useAuthStore } from '../stores/auth';
import { ticketsApi } from '../services/api';
import ChangePasswordForm from '../components/auth/ChangePasswordForm.vue';
import type { TicketSession } from '../types/tickets';
import { formatDateToLocaleString, formatDateLong } from '../utils/dateUtils';
import SessionDetailsDialog from '../components/dialogs/SessionDetailsDialog.vue';
import { useCachedData } from '../composables/useCachedData';
import { CACHE_KEYS } from '../services/cacheService';

const authStore = useAuthStore();
const user = computed(() => authStore.user);

const {
  data: sessions,
  isLoading: isLoadingSessions,
  loadData: loadSessions,
  refreshData: refreshSessions
} = useCachedData<TicketSession[]>({
  cacheKey: CACHE_KEYS.PROFILE_SESSIONS,
  fetchFunction: async () => {
    if (!authStore.isAuthenticated) return [];

    const allSessions = await ticketsApi.getSessions();
    return allSessions.filter(session => session.userId === user.value?.id);
  },
  showUpdateNotification: false
});

const initialLoading = ref(true);
const selectedSession = ref<TicketSession | null>(null);



const completedSessions = computed(() => {
  if (!sessions.value) return 0;
  return sessions.value.filter(session => session.status === 'completed').length;
});

const totalTickets = computed(() => {
  if (!sessions.value) return 0;
  return sessions.value.reduce((total, session) => total + session.processed, 0);
});

const recentSessions = computed(() => {
  if (!sessions.value) return [];
  return [...sessions.value]
    .sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return dateB - dateA;
    })
    .slice(0, 5);
});

watch(() => sessions.value, () => {
  console.log('Sessions data updated');
});

function getStatusText(status: string): string {
  switch (status) {
    case 'created':
      return 'Створено';
    case 'started':
      return 'Розпочато';
    case 'processing':
      return 'В процесі';
    case 'processed':
      return 'Оброблено';
    case 'waiting':
      return 'Очікування';
    case 'completed':
      return 'Завершено';
    case 'failed':
      return 'Збій';
    case 'error':
      return 'Помилка';
    case 'cancelled':
      return 'Скасовано';
    case 'paused':
      return 'Призупинено';
    case 'resumed':
      return 'Відновлено';
    default:
      return 'Невідомо';
  }
}

function viewSessionDetails(sessionId: string) {
  const sessionFromList = sessions.value?.find(s => s.sessionId === sessionId);

  if (sessionFromList) {
    selectedSession.value = { ...sessionFromList, isOwner: true };

    setTimeout(async () => {
      try {
        const session = await ticketsApi.getSessionById(sessionId);
        if (selectedSession.value && selectedSession.value.sessionId === sessionId) {
          selectedSession.value = { ...session, isOwner: true };
        }
      } catch (err) {
        console.error('Error fetching session details:', err);
      }
    }, 100);
  } else {
    selectedSession.value = {
      sessionId,
      status: 'processing',
      total: 0,
      processed: 0,
      events: [],
      isOwner: true
    };

    setTimeout(async () => {
      try {
        const session = await ticketsApi.getSessionById(sessionId);
        if (selectedSession.value && selectedSession.value.sessionId === sessionId) {
          selectedSession.value = { ...session, isOwner: true };
        }
      } catch (err) {
        console.error('Error fetching session details:', err);
        if (selectedSession.value && selectedSession.value.sessionId === sessionId) {
          selectedSession.value = null;
        }
      }
    }, 100);
  }
}

function closeSessionDetails() {
  selectedSession.value = null;
}

watch(() => sessions.value, (newSessions) => {
  if (newSessions) {
    initialLoading.value = false;
  }
}, { immediate: true });

onMounted(async () => {
  await loadSessions();
});
</script>

<style scoped>
.stats-item, .session-item {
  transition: all 0.3s ease-in-out;
}
.session-item:hover {
  background-color: transparent !important;
}

.grid>div {
  transition: none;
}
</style>
