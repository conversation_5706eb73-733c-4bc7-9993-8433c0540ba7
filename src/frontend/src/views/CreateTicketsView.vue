<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div v-if="isCheckingSession && (hasSessionInUrl || hasSessionInLocalStorage || ticketsStore.currentSession)" class="mt-1">
      <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <div class="flex items-center gap-3">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
              Створення тікетів
            </h3>
            <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse"></div>
          </div>
          <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
            ID: <span class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-40 inline-block animate-pulse"></span>
          </p>
          <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
            ℹ️ Можна поки піти попити чаю, а тікети створяться самі. Сторінку можна закривати, якщо вона вам заважає. Повернутись до споглядання процесу створення тікетів можна будь-коли.
          </p>
        </div>

        <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
          <div class="mb-4">
            <div class="flex justify-between mb-1">
              <span class="text-base font-medium text-gray-700 dark:text-gray-300">
                <span class="h-5 bg-gray-200 dark:bg-gray-700 rounded w-24 inline-block animate-pulse"></span>
              </span>
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                <span class="h-5 bg-gray-200 dark:bg-gray-700 rounded w-12 inline-block animate-pulse"></span>
              </span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700"></div>
          </div>

          <div class="flex items-center justify-end">
            <div class="flex space-x-4">
              <div class="relative w-12 h-10">
                <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded w-12 animate-pulse"></div>
              </div>
              <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded w-12 animate-pulse"></div>
            </div>
          </div>
        </div>

        <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
          <div class="flex justify-between items-center mb-4">
            <h4 class="text-md font-medium text-gray-900 dark:text-white">Останні події</h4>
          </div>
          <div class="max-h-60 overflow-y-auto">
            <div class="mb-2 p-2 border-l-4 rounded-r-md border-gray-200 dark:border-gray-700 animate-pulse">
              <div class="flex items-center">
                <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20 mr-2"></div>
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              </div>
              <div class="mt-1">
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
              </div>
            </div>
            <div class="mb-2 p-2 border-l-4 rounded-r-md border-gray-200 dark:border-gray-700 animate-pulse">
              <div class="flex items-center">
                <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20 mr-2"></div>
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
              </div>
              <div class="mt-1">
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
              </div>
            </div>
            <div class="mb-2 p-2 border-l-4 rounded-r-md border-gray-200 dark:border-gray-700 animate-pulse">
              <div class="flex items-center">
                <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20 mr-2"></div>
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
              <div class="mt-1">
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
              </div>
            </div>
            <div class="mb-2 p-2 border-l-4 rounded-r-md border-gray-200 dark:border-gray-700 animate-pulse">
              <div class="flex items-center">
                <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20 mr-2"></div>
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/5"></div>
              </div>
              <div class="mt-1">
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="ticketsStore.currentSession" class="mt-1">
      <SessionProgress
        :session="ticketsStore.currentSession"
        @reset="resetSession"
        @pause="pauseProcess"
        @resume="resumeProcess"
        @showCancelDialog="toggleCancelDialog"
        @viewDetails="viewSessionDetails"
      />

      <CancelConfirmationDialog
        :show="showCancelConfirmation"
        :is-loading="isCancelLoading"
        @confirm="confirmCancel"
        @cancel="toggleCancelDialog"
      />

      <SessionDetailsDialog
        v-if="selectedSession"
        :show="!!selectedSession"
        :session="selectedSession"
        :showManageButton="false"
        @close="closeSessionDetails"
      />
    </div>

    <div v-else-if="!isCheckingSession" class="mt-1">
      <TicketForm
        :form-data="formData"
        :is-loading="ticketsStore.isLoading"
        :error="ticketsStore.error"
        @submit="submitForm"
        @showResetDialog="toggleResetDialog"
      />

      <ResetConfirmationDialog
        :show="showResetConfirmation"
        @confirm="handleResetConfirm"
        @cancel="toggleResetDialog"
      />

      <CreateConfirmationDialog
        :show="showCreateConfirmation"
        :has-subscription-id="!!formData.SubscriptionId && formData.SubscriptionId.trim() !== ''"
        :is-loading="ticketsStore.isLoading"
        @confirm="confirmSubmitForm"
        @cancel="toggleCreateDialog"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onActivated } from 'vue';
import { useRoute } from 'vue-router';
import { useTicketsStore } from '../stores/tickets';
import type { TicketFormData, TicketSession } from '../types/tickets';

import SessionProgress from '../components/status/SessionProgress.vue';
import TicketForm from '../components/forms/TicketForm.vue';
import CancelConfirmationDialog from '../components/dialogs/CancelConfirmationDialog.vue';
import ResetConfirmationDialog from '../components/dialogs/ResetConfirmationDialog.vue';
import CreateConfirmationDialog from '../components/dialogs/CreateConfirmationDialog.vue';
import SessionDetailsDialog from '../components/dialogs/SessionDetailsDialog.vue';

import { useFormValidation } from '../composables/useFormValidation';
import { useLocalStorage } from '../composables/useLocalStorage';
import { useSessionStatus } from '../composables/useSessionStatus';
import { useConfirmationDialog } from '../composables/useConfirmationDialog';

const ticketsStore = useTicketsStore();
const isCheckingSession = ref(true);
const hasSessionInUrl = ref(false);
const hasSessionInLocalStorage = ref(false);
const selectedSession = ref<TicketSession | null>(null);

const defaultFormValues: TicketFormData = {
  MSISDN: '',
  SubscriptionId: '',
  CategoryName: 'Домашний Интернет/Segment FTTB/Процедурні послуги/Анулювання договору/Анулювання договору (ФТТБ)/Інше',
  VirtualTeamName: 'Virtual Team',
  ReactionName: 'Звернення збережено з реакцією на технічного спеціаліста',
  ComplainDetails: `Рахунок Домашнього Інтернету анульований...`,
  Title: 'Анулювання договору FTTB',
  ResolutionSummary: `Рахунок Домашнього Інтернету анульований...`,
  TickerStatus: '2',
  NotificationType: '3',
  InformSubscriber: '4',
  ResolutionReason: '19'
};

const formData = ref<TicketFormData>({
  ...defaultFormValues
});

const { validateForm, processFormData } = useFormValidation(formData.value);
const { loadFormDataFromLocalStorage, resetFormToDefaults } = useLocalStorage(formData.value, defaultFormValues);
const {
  isCancelLoading
} = useSessionStatus(ticketsStore.currentSession);
const {
  showCancelConfirmation,
  showResetConfirmation,
  showCreateConfirmation,
  toggleCancelDialog,
  toggleResetDialog,
  toggleCreateDialog
} = useConfirmationDialog();

function submitForm() {
  if (validateForm()) {
    toggleCreateDialog();
  }
}

async function confirmSubmitForm() {
  const processedData = processFormData();
  toggleCreateDialog();
  await ticketsStore.createTickets(processedData);
}

async function confirmCancel() {
  if (ticketsStore.currentSession) {
    isCancelLoading.value = true;
    try {
      await ticketsStore.cancelTicketCreation(ticketsStore.currentSession.sessionId);
      toggleCancelDialog();
    } finally {
      isCancelLoading.value = false;
    }
  }
}

function pauseProcess(sessionId: string) {
  ticketsStore.pauseTicketCreation(sessionId);
}

function resumeProcess(sessionId: string) {
  ticketsStore.resumeTicketCreation(sessionId);
}

function resetSession() {
  ticketsStore.resetSession();
}

function handleResetConfirm() {
  resetFormToDefaults();
  toggleResetDialog();
}

function viewSessionDetails(sessionId: string) {
  if (ticketsStore.currentSession && ticketsStore.currentSession.sessionId === sessionId) {
    selectedSession.value = { ...ticketsStore.currentSession };

    setTimeout(async () => {
      try {
        const session = await ticketsStore.getSessionStatus(sessionId);
        if (selectedSession.value && selectedSession.value.sessionId === sessionId) {
          selectedSession.value = session;
        }
      } catch (err) {
        console.error('Error fetching session details:', err);
      }
    }, 100);
  } else {
    selectedSession.value = {
      sessionId,
      status: 'processing',
      total: 0,
      processed: 0,
      events: []
    };

    setTimeout(async () => {
      try {
        const session = await ticketsStore.getSessionStatus(sessionId);
        if (selectedSession.value && selectedSession.value.sessionId === sessionId) {
          selectedSession.value = session;
        }
      } catch (err) {
        console.error('Error fetching session details:', err);
        if (selectedSession.value && selectedSession.value.sessionId === sessionId) {
          selectedSession.value = null;
        }
      }
    }, 100);
  }
}

function closeSessionDetails() {
  selectedSession.value = null;
}

const route = useRoute();

onMounted(async () => {
  isCheckingSession.value = true;

  try {
    const sessionIdFromUrl = route.query.sessionId as string;
    hasSessionInUrl.value = !!sessionIdFromUrl;

    const savedSessionId = localStorage.getItem("currentSessionId");
    hasSessionInLocalStorage.value = !!savedSessionId;

    if (ticketsStore.currentSession) {
      const isOwned = ticketsStore.currentSession.isOwner ||
                     ticketsStore.isSessionOwner(ticketsStore.currentSession);

      if (!isOwned) {
        console.log('Current session is not owned by current user, resetting');
        ticketsStore.resetSession();
        localStorage.removeItem("currentSessionId");
        hasSessionInLocalStorage.value = false;
      }
    }

    if (sessionIdFromUrl) {
      console.log(`Found sessionId in URL: ${sessionIdFromUrl}, attempting to load`);
      try {
        const session = await ticketsStore.getSessionStatus(sessionIdFromUrl);
        if (session && !["completed", "failed", "error", "cancelled"].includes(session.status)) {
          if (session.isOwner || ticketsStore.isSessionOwner(session)) {
            console.log(`Session ${sessionIdFromUrl} is valid and owned by current user, connecting`);
            localStorage.setItem("currentSessionId", sessionIdFromUrl);
            hasSessionInLocalStorage.value = true;

            if (session.status === "resumed" || session.status === "processing") {
              session.paused = false;
              console.log(`Setting paused flag to false for ${session.status} session`);

              if (session.status === "resumed") {
                console.log(`Preserving resumed status in CreateTicketsView`);
              }
            } else if (session.status === "paused") {
              session.paused = true;
              console.log(`Setting paused flag to true for paused session`);
            }

            if (session.status === "paused" && !session.events.some(e => e.status === "paused")) {
              const pauseEvent = {
                status: "paused" as const,
                total: session.total,
                processed: session.processed,
                message: "Processing paused by user request",
                timestamp: new Date().toISOString(),
              };
              console.log(`Manually adding missing pause event`);
              session.events.push(pauseEvent);
            } else if (session.status === "resumed" && !session.events.some(e => e.status === "resumed")) {
              const resumeEvent = {
                status: "resumed" as const,
                total: session.total,
                processed: session.processed,
                message: "Processing resumed by user request",
                timestamp: new Date().toISOString(),
              };
              console.log(`Manually adding missing resume event`);
              session.events.push(resumeEvent);
            }

            ticketsStore.currentSession = session;
            ticketsStore.connectToEventStream(sessionIdFromUrl);
          } else {
            console.log(`Session ${sessionIdFromUrl} is not owned by current user`);
            ticketsStore.resetSession();
          }
        } else if (session) {
          console.log(`Session ${sessionIdFromUrl} is in final state (${session.status}), not connecting`);
          hasSessionInUrl.value = false;
        } else {
          console.log(`Session ${sessionIdFromUrl} not found`);
          hasSessionInUrl.value = false;
        }
      } catch (err) {
        console.error(`Error loading session from URL parameter: ${sessionIdFromUrl}`, err);
        hasSessionInUrl.value = false;
      }
    } else {
      const hasActiveSession = await ticketsStore.checkForActiveSession();
      hasSessionInLocalStorage.value = hasActiveSession;
    }
  } catch (err) {
    console.error('Error in CreateTicketsView onMounted:', err);
    hasSessionInUrl.value = false;
    hasSessionInLocalStorage.value = false;
  } finally {
    isCheckingSession.value = false;
    loadFormDataFromLocalStorage();
  }
});

onActivated(async () => {
  if (!isCheckingSession.value && ticketsStore.currentSession) {
    console.log('CreateTicketsView activated, refreshing current session');

    try {
      const sessionId = ticketsStore.currentSession.sessionId;
      const session = await ticketsStore.getSessionStatus(sessionId);

      if (session && !["completed", "failed", "cancelled"].includes(session.status)) {
        const wasPaused = ticketsStore.currentSession.paused;

        if (session.status === "paused") {
          session.paused = true;
        } else if (session.status === "resumed" || session.status === "processing") {
          session.paused = false;
        } else {
          session.paused = wasPaused;
        }

        ticketsStore.currentSession = session;
        console.log('CreateTicketsView session updated:', session.status, 'paused:', session.paused);

        if (!["completed", "failed", "cancelled"].includes(session.status)) {
          ticketsStore.connectToEventStream(sessionId);
        }
      }
    } catch (err) {
      console.error('Error refreshing session on activation:', err);
    }
  }
});
</script>
