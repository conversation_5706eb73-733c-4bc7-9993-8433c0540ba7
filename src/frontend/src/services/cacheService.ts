import { get, set, del, createStore } from 'idb-keyval';

const cacheStore = createStore("cm-echo-cache", "app-cache");

export const CACHE_KEYS = {
  SESSIONS: "sessions",
  PROFILE_SESSIONS: "profile-sessions",
  PROFILE: "profile",
  SESSION_DETAILS: "session-details",
  SESSION_EVENTS: "session-events",
};

export function getSessionDetailsKey(sessionId: string): string {
  return `${CACHE_KEYS.SESSION_DETAILS}-${sessionId}`;
}

export function getSessionEventsKey(sessionId: string, limit: number, offset: number): string {
  return `${CACHE_KEYS.SESSION_EVENTS}-${sessionId}-${limit}-${offset}`;
}

const CACHE_EXPIRATION = {
  SESSIONS: 5 * 60 * 1000,
  PROFILE: 5 * 60 * 1000,
  SESSION_DETAILS: 5 * 60 * 1000,
  SESSION_EVENTS: 5 * 60 * 1000,
};

interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

export async function setCache<T>(key: string, data: T): Promise<void> {
  const cacheEntry: CacheEntry<T> = {
    data,
    timestamp: Date.now(),
  };

  try {
    await set(key, cacheEntry, cacheStore);
  } catch (error) {
    console.error(`Error setting cache for ${key}:`, error);
  }
}

export async function getCache<T>(key: string): Promise<T | null> {
  try {
    const cacheEntry = await get<CacheEntry<T>>(key, cacheStore);

    if (!cacheEntry) {
      return null;
    }

    return cacheEntry.data;
  } catch (error) {
    console.error(`Error getting cache for ${key}:`, error);
    return null;
  }
}

export async function isCacheValid(key: string): Promise<boolean> {
  try {
    const cacheEntry = await get<CacheEntry<any>>(key, cacheStore);

    if (!cacheEntry) {
      return false;
    }

    const now = Date.now();
    const expirationTime = getExpirationTime(key);

    return now - cacheEntry.timestamp < expirationTime;
  } catch (error) {
    console.error(`Error checking cache validity for ${key}:`, error);
    return false;
  }
}

export async function getCacheTimestamp(key: string): Promise<number | null> {
  try {
    const cacheEntry = await get<CacheEntry<any>>(key, cacheStore);

    if (!cacheEntry) {
      return null;
    }

    return cacheEntry.timestamp;
  } catch (error) {
    console.error(`Error getting cache timestamp for ${key}:`, error);
    return null;
  }
}

export async function clearCache(key: string): Promise<void> {
  try {
    await del(key, cacheStore);
  } catch (error) {
    console.error(`Error clearing cache for ${key}:`, error);
  }
}

function getExpirationTime(key: string): number {
  if (key.startsWith(`${CACHE_KEYS.SESSION_DETAILS}-`)) {
    return CACHE_EXPIRATION.SESSION_DETAILS;
  }

  if (key.startsWith(`${CACHE_KEYS.SESSION_EVENTS}-`)) {
    return CACHE_EXPIRATION.SESSION_EVENTS;
  }

  switch (key) {
    case CACHE_KEYS.SESSIONS:
      return CACHE_EXPIRATION.SESSIONS;
    case CACHE_KEYS.PROFILE_SESSIONS:
      return CACHE_EXPIRATION.SESSIONS;
    case CACHE_KEYS.PROFILE:
      return CACHE_EXPIRATION.PROFILE;
    default:
      return 5 * 60 * 1000;
  }
}
