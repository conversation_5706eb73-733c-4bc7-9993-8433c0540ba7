import axios from 'axios';
import type { TicketFormData, TicketSession } from '../types/tickets';
import type { User } from '../types/user';

const apiClient = axios.create({
  baseURL: '/cm-echo/api',
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

apiClient.interceptors.response.use(
  response => response,
  error => {
    console.error("API Error:", error);

    let errorMessage = "Сталася помилка при виконанні запиту";

    if (error.response) {
      if (error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      } else if (error.response.status === 401) {
        errorMessage = "Неправильний логін або пароль";
      } else if (error.response.status === 403) {
        errorMessage =
          "Обліковий запис заблоковано через занадто багато невдалих спроб входу.";
      }
    } else if (error.request) {
      errorMessage = "Немає відповіді від сервера. Перевірте підключення до мережі";
    }

    error.message = errorMessage;
    return Promise.reject(error);
  }
);

export const authApi = {
  checkAuth: async (): Promise<User> => {
    const response = await apiClient.get("/auth/me");
    if (response.data.status === "success") {
      return response.data.user;
    }
    throw new Error(response.data.message || "Помилка авторизації");
  },

  login: async (username: string, password: string): Promise<User> => {
    const response = await apiClient.post("/auth/login", { username, password });
    if (response.data.status === "success") {
      return response.data.user;
    }
    throw new Error(response.data.message || "Помилка авторизації");
  },

  logout: async (): Promise<void> => {
    await apiClient.post("/auth/logout");
  },

  requestSignup: async (email: string, text: string): Promise<boolean> => {
    const response = await apiClient.post("/auth/signup", { email, text });
    return response.data.status === "success";
  },

  changePassword: async (currentPassword: string, newPassword: string): Promise<boolean> => {
    const response = await apiClient.post("/auth/change-password", {
      currentPassword,
      newPassword,
    });
    return response.data.status === "success";
  },

  requestPasswordReset: async (email: string): Promise<boolean> => {
    const response = await apiClient.post("/auth/request-reset", { email });
    return response.data.status === "success";
  },

  resetPassword: async (token: string, newPassword: string): Promise<boolean> => {
    const response = await apiClient.post("/auth/reset-password", { token, newPassword });
    return response.data.status === "success";
  },

  getUsers: async (): Promise<User[]> => {
    const response = await apiClient.get("/users");
    if (response.data.status === "success") {
      return response.data.users;
    }
    throw new Error(response.data.message || "Помилка отримання списку користувачів");
  },
};

export const ticketsApi = {
  createTickets: async (
    formData: TicketFormData
  ): Promise<{ sessionId: string; total?: number; processed?: number }> => {
    const response = await apiClient.post("/create-tickets", formData);
    if (response.data.status === "processing_started") {
      return {
        sessionId: response.data.sessionId,
        total: response.data.total || 0,
        processed: response.data.processed || 0,
      };
    }
    throw new Error(response.data.message || "Помилка при створенні тікетів");
  },

  cancelTicketCreation: async (sessionId: string): Promise<boolean> => {
    const response = await apiClient.delete(`/create-tickets/${sessionId}`);
    return response.data.status === "cancelled";
  },

  pauseTicketCreation: async (sessionId: string): Promise<boolean> => {
    const response = await apiClient.post(`/create-tickets/pause/${sessionId}`);
    return response.data.status === "paused";
  },

  resumeTicketCreation: async (sessionId: string): Promise<boolean> => {
    const response = await apiClient.post(`/create-tickets/resume/${sessionId}`);
    return response.data.status === "resumed";
  },

  getSessions: async (): Promise<TicketSession[]> => {
    const response = await apiClient.get("/sessions");
    if (response.data.status === "success") {
      return response.data.sessions;
    }
    throw new Error(response.data.message || "Помилка завантаження сесій");
  },

  getActiveSessions: async (): Promise<TicketSession[]> => {
    const response = await apiClient.get("/sessions/active");
    if (response.data.status === "success") {
      return response.data.sessions;
    }
    throw new Error(response.data.message || "Помилка завантаження активних сесій");
  },

  getSessionById: async (
    sessionId: string,
    limit: number = 0,
    offset: number = 0
  ): Promise<TicketSession> => {
    const response = await apiClient.get(`/sessions/${sessionId}`, {
      params: { limit, offset },
    });
    if (response.data.status === "success") {
      return response.data.session;
    }
    throw new Error(response.data.message || "Помилка завантаження сесії");
  },

  getSessionEvents: async (
    sessionId: string,
    limit: number = 0,
    offset: number = 0
  ): Promise<{ events: any[]; totalEvents: number; offset: number; limit: number }> => {
    const response = await apiClient.get(`/sessions/${sessionId}`, {
      params: { limit, offset, eventsOnly: true },
    });
    if (response.data.status === "success") {
      return {
        events: response.data.events,
        totalEvents: response.data.totalEvents,
        offset: response.data.offset,
        limit: response.data.limit,
      };
    }
    throw new Error(response.data.message || "Помилка завантаження подій сесії");
  },
};

export default {
  auth: authApi,
  tickets: ticketsApi
};
