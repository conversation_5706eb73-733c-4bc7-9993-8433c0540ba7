@layer components {
  .dialog-backdrop {
    @apply fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity;
    @apply dark:bg-gray-900 dark:bg-opacity-75;
  }

  .dialog-container {
    @apply fixed inset-0 z-10 overflow-y-auto;
  }

  .dialog-position {
    @apply flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0;
  }

  .dialog-panel {
    @apply relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all;
    @apply dark:bg-gray-800;
    @apply sm:my-8 sm:w-full sm:max-w-lg;
  }

  .dialog-content {
    @apply px-4 pt-5 pb-4 sm:p-6;
  }

  .dialog-header {
    @apply mb-4 text-center sm:text-left;
  }

  .dialog-title {
    @apply text-lg font-medium leading-6 text-gray-900;
    @apply dark:text-white;
  }

  .dialog-body {
    @apply mt-2;
  }

  .dialog-text {
    @apply text-sm text-gray-500;
    @apply dark:text-gray-300;
  }

  .dialog-footer {
    @apply bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6;
    @apply dark:bg-gray-700;
  }

  .dialog-btn {
    @apply inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold shadow-sm;
    @apply sm:ml-3 sm:w-auto;
  }

  .dialog-btn-confirm {
    @apply bg-primary-600 text-white hover:bg-primary-500;
    @apply dark:bg-primary-700 dark:hover:bg-primary-600;
  }

  .dialog-btn-cancel {
    @apply bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50;
    @apply dark:bg-gray-800 dark:text-gray-100 dark:ring-gray-600 dark:hover:bg-gray-700;
  }

  .dialog-btn-danger {
    @apply bg-red-600 text-white hover:bg-red-500;
    @apply dark:bg-red-700 dark:hover:bg-red-600;
  }

  .dialog-icon {
    @apply mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full;
    @apply sm:mx-0 sm:h-10 sm:w-10;
  }

  .dialog-icon-info {
    @apply bg-blue-100;
    @apply dark:bg-blue-900;
  }

  .dialog-icon-success {
    @apply bg-green-100;
    @apply dark:bg-green-900;
  }

  .dialog-icon-warning {
    @apply bg-yellow-100;
    @apply dark:bg-yellow-900;
  }

  .dialog-icon-danger {
    @apply bg-red-100;
    @apply dark:bg-red-900;
  }

  .dialog-icon svg {
    @apply h-6 w-6;
  }

  .dialog-icon-info svg {
    @apply text-blue-600;
    @apply dark:text-blue-400;
  }

  .dialog-icon-success svg {
    @apply text-green-600;
    @apply dark:text-green-400;
  }

  .dialog-icon-warning svg {
    @apply text-yellow-600;
    @apply dark:text-yellow-400;
  }

  .dialog-icon-danger svg {
    @apply text-red-600;
    @apply dark:text-red-400;
  }

  .dialog-enter-active,
  .dialog-leave-active {
    @apply transition-opacity duration-300;
  }

  .dialog-enter-from,
  .dialog-leave-to {
    @apply opacity-0;
  }
}
