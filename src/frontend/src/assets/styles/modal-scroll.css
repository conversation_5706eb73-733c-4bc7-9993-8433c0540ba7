html {
  scrollbar-gutter: stable;
}

body {
  overflow-y: scroll;
}

.modal-open {
  overflow: hidden !important;
  padding-right: var(--scrollbar-width, 17px) !important;
}

.modal-backdrop {
  width: calc(100vw - var(--scrollbar-width, 17px));
  right: 0;
  left: 0;
  margin-right: var(--scrollbar-width, 17px);
}

.events-container {
  overflow-y: auto !important;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  outline: none;
  cursor: default;
  border-radius: 0.375rem;
}

.modal-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow-y: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 42rem;
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dark .modal-content {
  background-color: #1f2937;
}
