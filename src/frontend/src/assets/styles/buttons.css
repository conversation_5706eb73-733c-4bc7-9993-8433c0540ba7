@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors;
    @apply focus:outline-none focus:ring-1 focus:ring-offset-0 focus:ring-primary-500/20;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-600;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600;
  }

  .btn-purple {
    @apply bg-purple-600 text-white hover:bg-purple-700 dark:bg-purple-700 dark:hover:bg-purple-600;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg;
  }

  .btn-icon {
    @apply inline-flex items-center justify-center;
  }

  .btn-icon-left svg {
    @apply -ml-1 mr-2 h-5 w-5;
  }

  .btn-icon-right svg {
    @apply -mr-1 ml-2 h-5 w-5;
  }

  .btn-icon-only {
    @apply p-2;
  }

  .btn-icon-only svg {
    @apply h-5 w-5;
  }

  .btn-loading {
    @apply relative text-opacity-0 transition-none;
  }

  .btn-loading::after {
    @apply absolute inset-0 flex items-center justify-center text-opacity-100;
    content: "Завантаження...";
  }

  .btn-group {
    @apply inline-flex rounded-md shadow-sm;
  }

  .btn-group .btn {
    @apply rounded-none;
  }

  .btn-group .btn:first-child {
    @apply rounded-l-md;
  }

  .btn-group .btn:last-child {
    @apply rounded-r-md;
  }

  .btn-link {
    @apply bg-transparent text-primary-600 hover:text-primary-700 hover:underline;
    @apply dark:text-primary-400 dark:hover:text-primary-300;
    @apply p-0 font-medium;
  }
}
