@layer components {
  .form-container {
    @apply space-y-6;
  }

  .form-section {
    @apply space-y-4 p-6 bg-white rounded-lg shadow-sm;
    @apply dark:bg-gray-800;
  }

  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700;
    @apply dark:text-gray-300;
  }

  .form-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm;
    @apply focus:border-primary-500 focus:ring-primary-500;
    @apply dark:bg-gray-700 dark:border-gray-600 dark:text-white;
    @apply disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed;
    @apply dark:disabled:bg-gray-800 dark:disabled:text-gray-400;
  }

  .form-textarea {
    @apply block w-full rounded-md border-gray-300 shadow-sm;
    @apply focus:border-primary-500 focus:ring-primary-500;
    @apply dark:bg-gray-700 dark:border-gray-600 dark:text-white;
    @apply disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed;
    @apply dark:disabled:bg-gray-800 dark:disabled:text-gray-400;
    @apply resize-y min-h-[100px];
  }

  .form-select {
    @apply block w-full rounded-md border-gray-300 shadow-sm;
    @apply focus:border-primary-500 focus:ring-primary-500;
    @apply dark:bg-gray-700 dark:border-gray-600 dark:text-white;
    @apply disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed;
    @apply dark:disabled:bg-gray-800 dark:disabled:text-gray-400;
  }

  .form-checkbox, .form-radio {
    @apply rounded border-gray-300 text-primary-600;
    @apply focus:ring-primary-500;
    @apply dark:border-gray-600 dark:bg-gray-700;
  }

  .form-radio {
    @apply rounded-full;
  }

  .form-error {
    @apply mt-1 text-sm text-red-600;
    @apply dark:text-red-400;
  }

  .form-helper {
    @apply mt-1 text-sm text-gray-500;
    @apply dark:text-gray-400;
  }

  .form-counter {
    @apply mt-1 text-sm text-gray-500 text-right;
    @apply dark:text-gray-400;
  }

  .form-actions {
    @apply flex items-center justify-end space-x-3 mt-6;
  }

  .form-input-valid {
    @apply border-green-500 pr-10;
    @apply dark:border-green-600;
  }

  .form-input-invalid {
    @apply border-red-500 pr-10;
    @apply dark:border-red-600;
  }

  .form-validation-icon {
    @apply absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none;
  }

  .form-field {
    @apply relative;
  }

  .form-field-with-icon {
    @apply relative;
  }

  .form-field-icon-left input {
    @apply pl-10;
  }

  .form-field-icon-right input {
    @apply pr-10;
  }

  .form-field-icon {
    @apply absolute inset-y-0 flex items-center pointer-events-none;
  }

  .form-field-icon-left .form-field-icon {
    @apply left-0 pl-3;
  }

  .form-field-icon-right .form-field-icon {
    @apply right-0 pr-3;
  }
}
