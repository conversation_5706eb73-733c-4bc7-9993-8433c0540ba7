@layer base {
  html {
    scrollbar-width: thin;
    scrollbar-color: theme("colors.gray.300") theme("colors.gray.100");
  }

  html.dark {
    scrollbar-color: theme("colors.gray.600") theme("colors.gray.800");
  }

  body {
    scrollbar-width: thin;
    scrollbar-color: theme("colors.gray.300") theme("colors.gray.100");
  }

  html.dark body {
    scrollbar-color: theme("colors.gray.600") theme("colors.gray.800");
  }

  html::-webkit-scrollbar,
  body::-webkit-scrollbar {
    @apply w-2.5 h-2.5;
  }

  html::-webkit-scrollbar-track,
  body::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  html.dark::-webkit-scrollbar-track,
  html.dark body::-webkit-scrollbar-track {
    @apply bg-gray-800;
  }

  html::-webkit-scrollbar-thumb,
  body::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
    @apply hover:bg-gray-400;
    @apply transition-colors duration-200;
    @apply border-2 border-transparent bg-clip-padding;
    @apply shadow-sm;
  }

  html.dark::-webkit-scrollbar-thumb,
  html.dark body::-webkit-scrollbar-thumb {
    @apply bg-gray-600 hover:bg-gray-500;
  }

  html::-webkit-scrollbar-thumb:active,
  body::-webkit-scrollbar-thumb:active {
    @apply bg-gray-500;
  }

  html.dark::-webkit-scrollbar-thumb:active,
  html.dark body::-webkit-scrollbar-thumb:active {
    @apply bg-gray-500;
  }
}

@layer components {
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }

  html.dark ::-webkit-scrollbar-track {
    @apply bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
    @apply hover:bg-gray-400;
    @apply transition-colors duration-200;
    @apply border-2 border-transparent bg-clip-padding;
  }

  html.dark ::-webkit-scrollbar-thumb {
    @apply bg-gray-600 hover:bg-gray-500;
  }

  ::-webkit-scrollbar-thumb:active {
    @apply bg-gray-500;
  }

  html.dark ::-webkit-scrollbar-thumb:active {
    @apply bg-gray-500;
  }

  ::-webkit-scrollbar-corner {
    @apply bg-transparent;
  }

  * {
    scrollbar-width: thin;
    scrollbar-color: theme("colors.gray.300") theme("colors.gray.100");
  }

  html.dark * {
    scrollbar-color: theme("colors.gray.600") theme("colors.gray.800");
  }

  .form-textarea {
    @apply scrollbar-thin;
  }

  .form-textarea::-webkit-scrollbar-thumb {
    @apply bg-gray-300;
  }

  .form-textarea::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  html.dark .form-textarea::-webkit-scrollbar-thumb {
    @apply bg-gray-600;
  }

  html.dark .form-textarea::-webkit-scrollbar-track {
    @apply bg-gray-800;
  }

  .dialog-content {
    @apply scrollbar-thin;
  }

  .dialog-content::-webkit-scrollbar-thumb {
    @apply bg-gray-300;
  }

  .dialog-content::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  html.dark .dialog-content::-webkit-scrollbar-thumb {
    @apply bg-gray-600;
  }

  html.dark .dialog-content::-webkit-scrollbar-track {
    @apply bg-gray-800;
  }

  .table-container {
    @apply scrollbar-thin;
  }

  .table-container::-webkit-scrollbar-thumb {
    @apply bg-gray-300;
  }

  .table-container::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  html.dark .table-container::-webkit-scrollbar-thumb {
    @apply bg-gray-600;
  }

  html.dark .table-container::-webkit-scrollbar-track {
    @apply bg-gray-800;
  }
}

@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 0.375rem;
    height: 0.375rem;
  }

  .scrollbar-none {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    @apply hidden;
  }

  .scrollbar-thumb-primary::-webkit-scrollbar-thumb {
    background-color: theme("colors.primary.500");
  }

  .scrollbar-thumb-primary::-webkit-scrollbar-thumb:hover {
    background-color: theme("colors.primary.600");
  }

  html.dark .scrollbar-thumb-primary::-webkit-scrollbar-thumb {
    background-color: theme("colors.primary.600");
  }

  html.dark .scrollbar-thumb-primary::-webkit-scrollbar-thumb:hover {
    background-color: theme("colors.primary.500");
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background-color: transparent;
  }
}
