.dp__main {
  @apply font-sans text-sm;
}

.dp__input_wrap {
  @apply bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-md shadow-sm focus-within:border-primary-500 focus-within:ring-primary-500 block w-full transition-colors;
  min-height: 40px;
}

.dp__input_wrap:hover {
  @apply bg-gray-200 dark:bg-gray-600;
}

.dp__input {
  @apply bg-transparent text-gray-900 dark:text-white text-sm py-0 px-2 h-10 border-0 focus:ring-0;
  margin-left: 30px;
}

.dp__input::placeholder {
  @apply text-gray-700 dark:text-gray-300;
}

.dp__menu {
  @apply border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-md shadow-sm z-50;
  margin-top: 4px;
}

.dp__calendar_header_item {
  @apply text-gray-700 dark:text-gray-300 font-medium;
}

.dp__calendar_item {
  @apply text-gray-900 dark:text-white;
}

.dp__calendar_item:hover {
  @apply bg-gray-200 dark:bg-gray-700;
}

.dp__active_date {
  @apply bg-primary-500 text-white;
}

.dp__date_hover {
  @apply bg-gray-200 dark:bg-gray-700 ;
}

.dp__today {
  @apply border-primary-500 dark:border-primary-400;
}

.dp__range_start,
.dp__range_end {
  @apply bg-primary-500 text-white;
}

.dp__range_between {
  @apply bg-primary-100 dark:bg-primary-900/30 text-gray-900 dark:text-white;
  border-color: #707070;
}

.dp__month_year_select {
  @apply text-gray-900 dark:text-white;
}

.dp__month_year_row {
  @apply bg-white dark:bg-gray-800;
}

.dp__month_year_select:hover {
  @apply bg-gray-200 dark:bg-gray-700;
}

.dp__action_buttons {
  @apply bg-white dark:bg-gray-800 border-t border-gray-300 dark:border-gray-600;
}

.dp__selection_preview {
  @apply text-gray-700 dark:text-gray-300;
}

.dp__action_button {
  @apply text-primary-600 dark:text-primary-400 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md px-2 py-1;
}

.dp__action_select {
  @apply bg-primary-500 text-white hover:bg-primary-600 dark:hover:bg-primary-400;
}

.dp__month_year_row button {
  @apply text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md;
}

.dp__overlay_cell_active {
  @apply bg-primary-500 text-white;
}

.dp__overlay_cell:hover {
  @apply bg-gray-200 dark:bg-gray-700;
}

.dp__arrow_top{
  display: none;
}

:root {
  --dp-font-family: inherit;
  --dp-border-radius: 0.375rem; 
  --dp-common-transition: all 0.2s ease-in-out;
  --dp-menu-min-width: 260px;
  --dp-font-size: 0.875rem;
  --dp-preview-font-size: 0.875rem;
}

.dp__theme_light {
  --dp-background-color: #fff;
  --dp-text-color: #111827;
  --dp-hover-color: #e5e7eb;
  --dp-hover-text-color: #111827;
  --dp-hover-icon-color: #6b7280;
  --dp-primary-color: #0ea5e9;
  --dp-primary-text-color: #fff;
  --dp-secondary-color: #e5e7eb;
  --dp-border-color: #bdbdbd;
  --dp-menu-border-color: #d1d5db;
  --dp-border-color-hover: #9ca3af;
  --dp-disabled-color: #f3f4f6;
  --dp-disabled-color-text: #6b7280;
  --dp-scroll-bar-background: #f3f4f6;
  --dp-scroll-bar-color: #9ca3af;
  --dp-success-color: #10b981;
  --dp-success-color-disabled: #a7f3d0;
  --dp-icon-color: #6b7280;
  --dp-danger-color: #ef4444;
  --dp-highlight-color: rgba(14, 165, 233, 0.1);
}

.dp__theme_dark {
  --dp-background-color: #1f2937;
  --dp-text-color: #f9fafb;
  --dp-hover-color: #374151;
  --dp-hover-text-color: #f9fafb;
  --dp-hover-icon-color: #9ca3af;
  --dp-primary-color: #0ea5e9;
  --dp-primary-text-color: #fff;
  --dp-secondary-color: #374151;
  --dp-border-color: #4b5563;
  --dp-menu-border-color: #4b5563;
  --dp-border-color-hover: #6b7280;
  --dp-disabled-color: #374151;
  --dp-disabled-color-text: #9ca3af;
  --dp-scroll-bar-background: #1f2937;
  --dp-scroll-bar-color: #4b5563;
  --dp-success-color: #10b981;
  --dp-success-color-disabled: #065f46;
  --dp-icon-color: #9ca3af;
  --dp-danger-color: #ef4444;
  --dp-highlight-color: rgba(14, 165, 233, 0.1);
}

.dp__input_icon {
  @apply text-gray-500 dark:text-gray-400;
}

.dp__icon:hover  {
  @apply text-gray-700 dark:text-gray-400;
}


