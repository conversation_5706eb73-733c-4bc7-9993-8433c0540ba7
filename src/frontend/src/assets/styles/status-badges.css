@layer components {
  .status-badge {
    @apply px-2 inline-flex text-xs leading-5 font-semibold rounded-full;
  }

  .status-badge-created,
  .status-badge-started,
  .status-badge-resumed {
    @apply bg-blue-100 text-blue-800;
  }

  .status-badge-processing {
    @apply bg-teal-100 text-teal-800;
  }

  .status-badge-waiting {
    @apply bg-yellow-100 text-yellow-800;
  }

  .status-badge-processed {
    @apply bg-indigo-100 text-indigo-800;
  }

  .status-badge-completed {
    @apply bg-green-100 text-green-800;
  }

  .status-badge-failed,
  .status-badge-error,
  .status-badge-cancelled {
    @apply bg-red-100 text-red-800;
  }

  .status-badge-paused {
    @apply bg-purple-100 text-purple-800;
  }

  .dark .status-badge-created,
  .dark .status-badge-started,
  .dark .status-badge-resumed {
    @apply bg-blue-900/30 text-blue-300;
  }

  .dark .status-badge-processing {
    @apply bg-teal-900/30 text-teal-300;
  }

  .dark .status-badge-waiting {
    @apply bg-yellow-900/30 text-yellow-300;
  }

  .dark .status-badge-processed {
    @apply bg-indigo-900/30 text-indigo-300;
  }

  .dark .status-badge-completed {
    @apply bg-green-900/30 text-green-300;
  }

  .dark .status-badge-failed,
  .dark .status-badge-error,
  .dark .status-badge-cancelled {
    @apply bg-red-900/30 text-red-300;
  }

  .dark .status-badge-paused {
    @apply bg-purple-900/30 text-purple-300;
  }

  .event-border-created,
  .event-border-started,
  .event-border-resumed {
    @apply border-blue-500 bg-blue-50;
  }

  .event-border-processing {
    @apply border-teal-500 bg-teal-50;
  }

  .event-border-waiting {
    @apply border-yellow-500 bg-yellow-50;
  }

  .event-border-processed {
    @apply border-indigo-500 bg-indigo-50;
  }

  .event-border-completed {
    @apply border-green-500 bg-green-50;
  }

  .event-border-failed,
  .event-border-error,
  .event-border-cancelled {
    @apply border-red-500 bg-red-50;
  }

  .event-border-paused {
    @apply border-purple-500 bg-purple-50;
  }

  .dark .event-border-created,
  .dark .event-border-started,
  .dark .event-border-resumed {
    @apply bg-blue-900/20;
  }

  .dark .event-border-processing {
    @apply bg-teal-900/20;
  }

  .dark .event-border-waiting {
    @apply bg-yellow-900/20;
  }

  .dark .event-border-processed {
    @apply bg-indigo-900/20;
  }

  .dark .event-border-completed {
    @apply bg-green-900/20;
  }

  .dark .event-border-failed,
  .dark .event-border-error,
  .dark .event-border-cancelled {
    @apply bg-red-900/20;
  }

  .dark .event-border-paused {
    @apply bg-purple-900/20;
  }

  .progress-bar-created,
  .progress-bar-started,
  .progress-bar-resumed {
    @apply bg-blue-500 shadow-md shadow-blue-500/20;
  }

  .progress-bar-processing {
    @apply bg-teal-500 shadow-md shadow-teal-500/20;
  }

  .progress-bar-waiting {
    @apply bg-yellow-500 shadow-md shadow-yellow-500/20;
  }

  .progress-bar-processed {
    @apply bg-indigo-500 shadow-md shadow-indigo-500/20;
  }

  .progress-bar-completed {
    @apply bg-green-500 shadow-md shadow-green-500/20;
  }

  .progress-bar-failed,
  .progress-bar-error,
  .progress-bar-cancelled {
    @apply bg-red-500 shadow-md shadow-red-500/20;
  }

  .progress-bar-paused {
    @apply bg-purple-500 shadow-md shadow-purple-500/20;
  }
}
