@import './styles/status-badges.css';
@import './styles/buttons.css';
@import './styles/forms.css';
@import './styles/dialogs.css';
@import './styles/scrollbars.css';
@import './styles/modal-scroll.css';
@import './styles/vue-datepicker.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
    @apply min-h-screen;
  }
}

@layer components {
  .input {
    @apply px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-800 dark:border-gray-700;
  }
}
