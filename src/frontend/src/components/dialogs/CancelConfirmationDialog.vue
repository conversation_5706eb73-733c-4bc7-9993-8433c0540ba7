<template>
  <ConfirmationDialog
    :show="show"
    title="Підтвердження скасування"
    message="Ви впевнені, що хочете скасувати процес створення тікетів? Ця дія є незворотною."
    confirm-text="Так"
    cancel-text="Ні"
    confirm-button-class="btn-danger"
    :is-loading="isLoading"
    @confirm="$emit('confirm')"
    @cancel="$emit('cancel')"
  />
</template>

<script setup lang="ts">
import ConfirmationDialog from './ConfirmationDialog.vue';

defineProps<{
  show: boolean;
  isLoading: boolean;
}>();

defineEmits<{
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}>();
</script>
