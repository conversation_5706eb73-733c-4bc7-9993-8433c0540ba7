<template>
  <ConfirmationDialog
    :show="show"
    title="Підтвердження"
    message="Ви впевнені, що хочете створити тікети з вказаними параметрами?"
    confirm-text="Так"
    cancel-text="Ні"
    confirm-button-class="btn-primary"
    :is-loading="isLoading"
    @confirm="$emit('confirm')"
    @cancel="$emit('cancel')"
  >
    <template v-slot:additional-content>
      <p v-if="!hasSubscriptionId" class="text-gray-700 dark:text-gray-300 mb-6 text-sm bg-yellow-100 dark:bg-yellow-900 p-2 rounded">
        Якщо це абоненти FMC або анульовані О/Р, потрібно обов'язково заповнити SubscriptionId — інакше тікети створюватимуться з О/Р 0000000000000
      </p>
      <p v-else class="mb-6"></p>
    </template>
  </ConfirmationDialog>
</template>

<script setup lang="ts">
import ConfirmationDialog from './ConfirmationDialog.vue';

defineProps<{
  show: boolean;
  hasSubscriptionId: boolean;
  isLoading: boolean;
}>();

defineEmits<{
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}>();
</script>
