<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 modal-backdrop"
    v-cloak>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full flex flex-col"
      style="max-height: 90vh;">
      <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
          Деталі сесії
        </h3>
        <button @click="handleClose" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6 flex-grow overflow-hidden">
        <div class="space-y-4">
          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Дата створення:</p>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">
              {{ formatDateToLocaleString(sessionDetails?.createdAt || props.session.createdAt) }}
            </p>
          </div>

          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">ID сесії:</p>
            <p class="mt-1 text-sm text-gray-900 dark:text-white break-all">
              {{ sessionDetails?.sessionId || props.session.sessionId }}
            </p>
          </div>

          <div v-if="(sessionDetails?.username || props.session.username)">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Користувач:</p>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">
              {{ sessionDetails?.username || props.session.username }}
            </p>
          </div>

          <div>
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Статус:</p>
            <div class="mt-1">
              <StatusBadge :status="sessionDetails?.status || props.session.status" />
            </div>
          </div>

          <div>
            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
              <div class="flex items-center">
                <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">Прогрес:</span>
                <span>{{ sessionDetails?.processed || props.session.processed }} / {{ sessionDetails?.total ||
                  props.session.total }}</span>
              </div>
              <span>{{ Math.round(((sessionDetails?.processed || props.session.processed) / (sessionDetails?.total ||
                props.session.total)) * 100) || 0 }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 relative z-10 overflow-hidden shadow-sm">
              <div class="h-1.5 rounded-full absolute top-0 left-0" :class="{
                'progress-bar-created': (sessionDetails?.status || props.session.status) === 'created' || (sessionDetails?.status || props.session.status) === 'started' || (sessionDetails?.status || props.session.status) === 'resumed',
                'progress-bar-processing': (sessionDetails?.status || props.session.status) === 'processing',
                'progress-bar-waiting': (sessionDetails?.status || props.session.status) === 'waiting',
                'progress-bar-processed': (sessionDetails?.status || props.session.status) === 'processed',
                'progress-bar-completed': (sessionDetails?.status || props.session.status) === 'completed',
                'progress-bar-failed': (sessionDetails?.status || props.session.status) === 'failed' || (sessionDetails?.status || props.session.status) === 'error' || (sessionDetails?.status || props.session.status) === 'cancelled',
                'progress-bar-paused': (sessionDetails?.status || props.session.status) === 'paused'
              }"
                :style="{ width: `${Math.round(((sessionDetails?.processed || props.session.processed) / (sessionDetails?.total || props.session.total)) * 100) || 0}%` }">
              </div>
            </div>
          </div>

          <div>
            <div class="flex justify-between items-center mb-2">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Журнал подій:</p>
              <p v-if="totalEvents > 0" class="text-xs text-gray-500 dark:text-gray-400">
                {{ filteredEventsCount }} з {{ totalEvents }} подій
              </p>
            </div>

            <div class="relative mb-2">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                </svg>
              </div>
              <input type="text" v-model="searchQuery" class="p-2 pl-10 input w-full"
                placeholder="Пошук по подіям..." />
              <div v-if="isSearching" class="absolute inset-y-0 right-10 flex items-center pr-3">
                <svg class="animate-spin h-4 w-4 text-gray-500 dark:text-gray-400" xmlns="http://www.w3.org/2000/svg"
                  fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
              </div>
              <button v-if="searchQuery" @click="clearSearch"
                class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div class="border border-gray-200 dark:border-gray-700 rounded-md events-container">
              <div v-if="isLoadingEvents" class="max-h-60 overflow-y-auto">
                <div v-for="i in 5" :key="i"
                  class="p-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0 animate-pulse">
                  <div class="flex items-center">
                    <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded-full w-20 mr-2"></div>
                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  </div>
                  <div class="flex justify-between mt-1">
                    <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                    <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
                  </div>
                </div>
              </div>

              <div v-else ref="eventsContainerRef" class="max-h-60 overflow-y-auto" @scroll="handleScroll">
                <template v-if="filteredEvents.length > 0">
                  <div v-for="(event, index) in filteredEvents" :key="index"
                    class="p-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0 text-sm" :class="{
                      'event-border-created': event.status === 'created' || event.status === 'started' || event.status === 'resumed',
                      'event-border-processing': event.status === 'processing',
                      'event-border-waiting': event.status === 'waiting',
                      'event-border-processed': event.status === 'processed',
                      'event-border-completed': event.status === 'completed',
                      'event-border-failed': event.status === 'failed' || event.status === 'error' || event.status === 'cancelled',
                      'event-border-paused': event.status === 'paused'
                    }">
                    <div class="flex items-center">
                      <StatusBadge :status="event.status" :inEventsList="true" class="mr-2" />
                      <p class="text-sm text-gray-700 dark:text-gray-300">{{ translateEventMessage(event.message) }}</p>
                    </div>
                    <div class="flex justify-between mt-1">
                      <p v-if="event.timestamp" class="text-xs text-gray-500 dark:text-gray-400">
                        {{ formatLogsDate(event.timestamp) }}
                      </p>
                      <p v-if="typeof event.processed === 'number' && typeof event.total === 'number'"
                        class="text-xs text-gray-500 dark:text-gray-400">
                        {{ event.processed }}/{{ event.total }}
                      </p>
                    </div>
                  </div>
                </template>

                <template
                  v-else-if="!searchQuery && !isLoadingEvents && displayedEvents.length === 0 && showNoEventsMessage && props.show">
                  <div class="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    Немає подій для відображення
                  </div>
                </template>

                <template v-else-if="searchQuery && displayedEvents.length > 0">
                  <div class="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    Нічого не знайдено за запитом "{{ searchQuery }}"
                  </div>
                </template>

                <div v-if="isLoadingMore && displayedEvents.length > 0" class="p-2 animate-pulse">
                  <div class="flex items-center">
                    <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded-full w-20 mr-2"></div>
                    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  </div>
                  <div class="flex justify-between mt-1">
                    <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                    <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="border-t border-gray-200 dark:border-gray-700 px-4 py-3 sm:px-6 flex justify-end space-x-3 bg-white dark:bg-gray-800 z-10">
        <router-link
          v-if="showManageButton && session.isOwner && !['completed', 'failed', 'error', 'cancelled'].includes(session.status)"
          :to="`/create-tickets?sessionId=${session.sessionId}`" class="btn btn-primary" @click="handleClose">
          Керувати
        </router-link>
        <button @click="handleClose" class="btn btn-secondary">
          Закрити
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue';

declare global {
  interface IdleRequestCallback {
    (deadline: IdleDeadline): void;
  }

  interface IdleRequestOptions {
    timeout?: number;
  }

  interface IdleDeadline {
    readonly didTimeout: boolean;
    timeRemaining(): DOMHighResTimeStamp;
  }

  interface Window {
    requestIdleCallback(callback: IdleRequestCallback, options?: IdleRequestOptions): number;
    cancelIdleCallback(handle: number): void;
  }
}
import type { TicketSession, TicketEvent } from '../../types/tickets';
import StatusBadge from '../status/StatusBadge.vue';
import { useScrollLock } from '../../composables/useScrollLock';
import { formatDateToLocaleString, formatLogsDate } from '../../utils/dateUtils';
import { translateEventMessage } from '../../utils/messageTranslations';
import { useSessionDetails } from '../../composables/useSessionDetails';
import { getCache, getSessionEventsKey } from '../../services/cacheService';
import { ticketsApi } from '../../services/api';

const props = defineProps<{
  show: boolean;
  session: TicketSession;
  showManageButton?: boolean;
}>();

const showManageButton = props.showManageButton !== false;

const emit = defineEmits<{
  (e: 'close'): void;
}>();

const { lockScroll, unlockScroll } = useScrollLock();


const sessionId = ref<string | null>(null);
const showNoEventsMessage = ref(false);
const {
  session: sessionDetails,
  events: displayedEvents,
  isLoadingEvents,
  isLoadingMoreEvents: isLoadingMore,
  currentOffset,
  hasMoreEvents,
  totalEvents,
  loadEvents,
  reset: resetSessionDetails
} = useSessionDetails(sessionId);

const eventsContainerRef = ref<HTMLElement | null>(null);
const batchSize = 50;

const searchQuery = ref('');
const isSearching = ref(false);
const allEvents = ref<TicketEvent[]>([]);

async function loadAllEvents() {
  if (!sessionId.value || !searchQuery.value.trim()) return;

  isSearching.value = true;
  try {
    let offset = 0;
    const events: TicketEvent[] = [];

    while (true) {
      const result = await ticketsApi.getSessionEvents(sessionId.value, 100, offset);
      events.push(...result.events);

      if (events.length >= result.totalEvents || result.events.length === 0) {
        break;
      }

      offset += 100;
    }

    allEvents.value = events;
  } catch (err) {
    console.error('Error loading all events for search:', err);
  } finally {
    isSearching.value = false;
  }
}

const filteredEvents = ref<TicketEvent[]>([]);

function updateFilteredEvents() {
  if (!displayedEvents.value) {
    filteredEvents.value = [];
    return;
  }

  if (!searchQuery.value.trim()) {
    filteredEvents.value = displayedEvents.value;
    allEvents.value = [];
    return;
  }

  const query = searchQuery.value.toLowerCase().trim();

  requestAnimationFrame(() => {
    if (allEvents.value.length > 0) {
      filteredEvents.value = allEvents.value.filter(event => {
        const translatedMessage = translateEventMessage(event.message).toLowerCase();
        const originalMessage = event.message.toLowerCase();
        const dateString = event.timestamp ? formatDateToLocaleString(event.timestamp).toLowerCase() : '';
        const status = event.status.toLowerCase();

        return translatedMessage.includes(query) ||
          originalMessage.includes(query) ||
          dateString.includes(query) ||
          status.includes(query);
      });
    } else {
      filteredEvents.value = displayedEvents.value.filter(event => {
        const translatedMessage = translateEventMessage(event.message).toLowerCase();
        const originalMessage = event.message.toLowerCase();
        const dateString = event.timestamp ? formatDateToLocaleString(event.timestamp).toLowerCase() : '';
        const status = event.status.toLowerCase();

        return translatedMessage.includes(query) ||
          originalMessage.includes(query) ||
          dateString.includes(query) ||
          status.includes(query);
      });

      loadAllEvents();
    }
  });
}

const filteredEventsCount = computed(() => {
  const actualCount = filteredEvents.value.length;

  if (searchQuery.value.trim()) {
    return actualCount;
  }

  return displayedEvents.value.length;
});

function clearSearch() {
  searchQuery.value = '';
  updateFilteredEvents();
}

watch(searchQuery, () => {
  updateFilteredEvents();
});

watch(displayedEvents, () => {
  updateFilteredEvents();
});


function loadEventsData(offset = 0, append = false) {
  requestAnimationFrame(async () => {
    try {
      await loadEvents(offset, append, batchSize);

      await nextTick();

      if (displayedEvents.value.length === 0 && !isLoadingEvents.value && offset === 0) {
        setTimeout(() => {
          showNoEventsMessage.value = true;
        }, 100);
      } else {
        showNoEventsMessage.value = false;
      }
    } catch (err) {
      console.error('Error loading events data:', err);
      isLoadingEvents.value = false;
      isLoadingMore.value = false;
    }
  });
}

let scrollTimeout: number | null = null;

function handleScroll() {
  if (!eventsContainerRef.value || isLoadingMore.value || !hasMoreEvents.value) return;

  if (scrollTimeout) {
    clearTimeout(scrollTimeout);
  }

  scrollTimeout = window.setTimeout(() => {
    const container = eventsContainerRef.value;
    if (!container) return;

    const scrollPosition = container.scrollTop + container.clientHeight;
    const scrollHeight = container.scrollHeight;

    if (scrollHeight - scrollPosition < 100) {
      loadEventsData(currentOffset.value, true);
    }

    scrollTimeout = null;
  }, 100);
}


watch(() => props.session.sessionId, (newSessionId) => {
  if (newSessionId) {
    sessionId.value = newSessionId;
  }
});

const handleEscKey = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    handleClose();
  }
};

watch(() => props.show, (newValue) => {
  if (newValue) {
    lockScroll();

    showNoEventsMessage.value = false;
    displayedEvents.value = [];
    isLoadingEvents.value = true;

    sessionId.value = props.session.sessionId;
    searchQuery.value = '';

    document.addEventListener('keydown', handleEscKey);

    requestAnimationFrame(async () => {
      try {
        if (!sessionId.value) return;

        const cacheKey = getSessionEventsKey(sessionId.value, batchSize, 0);
        const cachedData = await getCache<{
          events: TicketEvent[];
          totalEvents: number;
          offset: number;
          limit: number;
        }>(cacheKey);

        if (cachedData && cachedData.events && cachedData.events.length > 0) {
          isLoadingEvents.value = false;
          displayedEvents.value = cachedData.events;

          if (props.session && ['completed', 'cancelled', 'failed', 'error'].includes(props.session.status)) {
            totalEvents.value = cachedData.totalEvents;
          } else {
            totalEvents.value = Math.max(cachedData.totalEvents, displayedEvents.value.length);
          }
          currentOffset.value = displayedEvents.value.length;
          hasMoreEvents.value = currentOffset.value < totalEvents.value;

          if (window.requestIdleCallback) {
            window.requestIdleCallback(() => {
              loadEventsData(0);
            }, { timeout: 2000 });
          } else {
            setTimeout(() => {
              loadEventsData(0);
            }, 500);
          }
        } else {
          requestAnimationFrame(async () => {
            await loadEventsData(0);
            isLoadingEvents.value = false;

            await nextTick();
            if (displayedEvents.value.length === 0) {
              showNoEventsMessage.value = true;
            }
          });
        }

        const searchInput = document.querySelector('input[placeholder="Пошук по подіям..."]');
        if (searchInput instanceof HTMLInputElement) {
          searchInput.focus();
        }
      } catch (err) {
        console.error('Error loading session events:', err);
        isLoadingEvents.value = false;
      }
    });
  } else {
    unlockScroll();
    resetSessionDetails();
    sessionId.value = null;
    showNoEventsMessage.value = false;
    document.removeEventListener('keydown', handleEscKey);
  }

  return () => {
    document.removeEventListener('keydown', handleEscKey);
  };
}, { immediate: true });

function handleClose() {
  unlockScroll();
  emit('close');
}
</script>

<style>
[v-cloak] {
  display: none;
}
</style>
