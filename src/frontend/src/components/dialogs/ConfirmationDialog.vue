<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md w-full">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ title }}</h3>
      <p class="text-gray-700 dark:text-gray-300 mb-6">{{ message }}</p>
      <slot name="additional-content"></slot>
      <div class="flex justify-end space-x-3">
        <button
          @click="handleCancel"
          class="btn btn-secondary w-24 flex items-center justify-center"
          :disabled="isLoading"
        >
          <span>{{ cancelText }}</span>
        </button>
        <button
          @click="handleConfirm"
          class="btn w-24 flex items-center justify-center"
          :class="confirmButtonClass"
          :disabled="isLoading"
        >
          <div class="flex items-center justify-center">
            <span v-if="isLoading" class="mr-2">
              <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            <span>{{ confirmText }}</span>
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { useScrollLock } from '../../composables/useScrollLock';

const props = defineProps<{
  show: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmButtonClass?: string;
  isLoading?: boolean;
}>();

const emit = defineEmits<{
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}>();

const { lockScroll, unlockScroll } = useScrollLock();

watch(() => props.show, (newValue) => {
  if (newValue) {
    lockScroll();
  } else {
    unlockScroll();
  }
}, { immediate: true });

function handleConfirm() {
  unlockScroll();
  emit('confirm');
}

function handleCancel() {
  unlockScroll();
  emit('cancel');
}
</script>
