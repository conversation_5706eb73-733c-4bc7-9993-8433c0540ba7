<template>
  <ConfirmationDialog
    :show="show"
    title="Підтвердження"
    message="Ви впевнені, що бажаєте відновити стандартні значення полів?"
    confirm-text="Так"
    cancel-text="Ні"
    confirm-button-class="btn-primary"
    @confirm="$emit('confirm')"
    @cancel="$emit('cancel')"
  />
</template>

<script setup lang="ts">
import ConfirmationDialog from './ConfirmationDialog.vue';

defineProps<{
  show: boolean;
}>();

defineEmits<{
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}>();
</script>
