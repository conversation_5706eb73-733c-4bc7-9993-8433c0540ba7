<template>
  <nav class="bg-white dark:bg-gray-800 shadow-md">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex">
          <div class="flex-shrink-0 flex items-center">
            <router-link to="/" class="text-xl font-bold text-primary-600 dark:text-primary-400 flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                <path d="M9 14h6"></path>
                <path d="M9 10h6"></path>
                <path d="M12 18h.01"></path>
              </svg>
              CM Echo
            </router-link>
          </div>
          <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
            <router-link
              to="/"
              class="inline-flex items-center px-1 pt-1 border-b-2"
              :class="[
                $route.name === 'home'
                  ? 'border-primary-500 text-gray-900 dark:text-white'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white'
              ]"
            >
              Головна
            </router-link>
            <router-link
              to="/create-tickets"
              class="inline-flex items-center px-1 pt-1 border-b-2"
              :class="[
                $route.name === 'create-tickets'
                  ? 'border-primary-500 text-gray-900 dark:text-white'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white'
              ]"
            >
              Тікети
            </router-link>
            <router-link
              to="/history"
              class="inline-flex items-center px-1 pt-1 border-b-2"
              :class="[
                $route.name === 'sessions'
                  ? 'border-primary-500 text-gray-900 dark:text-white'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white'
              ]"
            >
              Історія
            </router-link>
            <a
              :href="docsFullUrl"
              target="_self"
              class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"
            >
              Документація
              <!-- <svg
                class="ml-1 h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
              </svg> -->
            </a>
          </div>
        </div>
        <div class="hidden sm:ml-6 sm:flex sm:items-center">
          <ThemeSwitcher class="mr-4" />

          <div v-if="authStore.isAuthenticated" class="ml-3 relative profile-menu">
            <div>
              <button
                @click="isProfileMenuOpen = !isProfileMenuOpen"
                class="flex text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <span class="sr-only">Відкрити меню користувача</span>
                <div class="h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center text-white">
                  {{ userInitials }}
                </div>
              </button>
            </div>

            <div
              v-if="isProfileMenuOpen"
              class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 focus:outline-none z-50 dark:shadow-[0_0_15px_rgba(0,0,0,0.3)]"
            >
              <div class="px-4 py-2 text-sm text-gray-700 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">
                {{ authStore.user?.username }}
              </div>
              <router-link
                to="/profile"
                @click="isProfileMenuOpen = false"
                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Профіль
              </router-link>
              <button
                @click="logout"
                class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                Вийти
              </button>
            </div>
          </div>

          <div v-else class="ml-3">
            <router-link
              to="/login"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Увійти
            </router-link>
          </div>
        </div>

        <div class="flex items-center sm:hidden">
          <ThemeSwitcher class="mr-4" />
          <button
            @click="isMobileMenuOpen = !isMobileMenuOpen"
            class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
          >
            <span class="sr-only">Відкрити головне меню</span>
            <svg
              class="h-6 w-6"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                v-if="!isMobileMenuOpen"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
              <path
                v-else
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <div v-if="isMobileMenuOpen" class="sm:hidden z-50 relative">
      <div class="pt-2 pb-3 space-y-1">
        <router-link
          to="/"
          class="block pl-3 pr-4 py-2 border-l-4"
          :class="[
            $route.name === 'home'
              ? 'border-primary-500 text-primary-700 bg-primary-50 dark:bg-gray-700 dark:text-primary-300'
              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 dark:text-gray-300 dark:hover:bg-gray-700'
          ]"
          @click="isMobileMenuOpen = false"
        >
          Головна
        </router-link>
        <router-link
          to="/create-tickets"
          class="block pl-3 pr-4 py-2 border-l-4"
          :class="[
            $route.name === 'create-tickets'
              ? 'border-primary-500 text-primary-700 bg-primary-50 dark:bg-gray-700 dark:text-primary-300'
              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 dark:text-gray-300 dark:hover:bg-gray-700'
          ]"
          @click="isMobileMenuOpen = false"
        >
          Тікети
        </router-link>
        <router-link
          to="/history"
          class="block pl-3 pr-4 py-2 border-l-4"
          :class="[
            $route.name === 'sessions'
              ? 'border-primary-500 text-primary-700 bg-primary-50 dark:bg-gray-700 dark:text-primary-300'
              : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 dark:text-gray-300 dark:hover:bg-gray-700'
          ]"
          @click="isMobileMenuOpen = false"
        >
          Історія
        </router-link>
        <a
          :href="docsFullUrl"
          target="_self"
          class="block pl-3 pr-4 py-2 border-l-4 border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 dark:text-gray-300 dark:hover:bg-gray-700"
          @click="isMobileMenuOpen = false"
        >
          <div class="flex items-center">
            Документація
            <!-- <svg
              class="ml-1 h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
              <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
            </svg> -->
          </div>
        </a>
      </div>
      <div class="pt-4 pb-3 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center px-4">
          <div class="flex-shrink-0">
            <div class="h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center text-white">
              {{ userInitials }}
            </div>
          </div>
          <div class="ml-3">
            <div class="text-base font-medium text-gray-800 dark:text-white">{{ authStore.user?.username }}</div>
            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ authStore.user?.email }}</div>
          </div>
        </div>
        <div class="mt-3 space-y-1">
          <router-link
            to="/profile"
            @click="isMobileMenuOpen = false"
            class="block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
          >
            Профіль
          </router-link>
          <button
            @click="logout"
            class="block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
          >
            Вийти
          </button>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../stores/auth';
import ThemeSwitcher from '../ui/ThemeSwitcher.vue';

const mode = import.meta.env.MODE
const docsUrl = mode === 'production'
  ? import.meta.env.VITE_PROD_URL || 'https://cca.kyivstar.ua'
  : import.meta.env.VITE_DEV_DOCS_URL || 'http://localhost:5174'
const docsFullUrl = `${docsUrl}/cm-echo/docs/`
const router = useRouter();
const authStore = useAuthStore();

const isMobileMenuOpen = ref(false);
const isProfileMenuOpen = ref(false);

const userInitials = computed(() => {
  if (!authStore.user) return '';

  const username = authStore.user.username;
  if (!username) return '';

  return username.charAt(0).toUpperCase();
});

async function logout() {
  await authStore.logout()
  isProfileMenuOpen.value = false
  isMobileMenuOpen.value = false
  router.push('/login')
}

function handleClickOutside(event: MouseEvent) {
  const target = event.target as HTMLElement
  if (!target.closest('.profile-menu')) {
    isProfileMenuOpen.value = false;
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>
