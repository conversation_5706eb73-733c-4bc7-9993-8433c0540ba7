<template>
  <div class="text-center py-8">
    <svg class="mx-auto h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Помилка завантаження</h3>
    <p class="mt-1 text-sm text-red-500">
      {{ error }}
    </p>
    <div class="mt-6">
      <button @click="$emit('retry')" class="btn btn-primary">
        Спробувати знову
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  error: string | null;
}>();

defineEmits<{
  (e: 'retry'): void;
}>();
</script>
