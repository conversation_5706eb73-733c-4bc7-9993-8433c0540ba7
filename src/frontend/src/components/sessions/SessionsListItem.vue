<template>
  <li class="px-4 py-4 sm:px-6 hover:bg-gray-100 dark:hover:bg-gray-700/70 relative session-item">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium truncate">
          <span class="text-primary-600 dark:text-primary-400 cursor-pointer hover:underline" @click="$emit('view-details', session.sessionId)">{{ session.sessionId }}</span>
        </p>
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {{ formatDateToLocaleString(session.createdAt) }}
        </p>
        <p v-if="session.username" class="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {{ session.username }}
        </p>
      </div>
      <div>
        <StatusBadge :status="session.status" />
      </div>
    </div>

    <div class="mt-2">
      <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
        <span>Прогрес: {{ session.processed }} / {{ session.total }}</span>
        <span>{{ Math.round((session.processed / session.total) * 100) || 0 }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 relative z-10 overflow-hidden shadow-sm">
        <div
          class="h-1.5 rounded-full absolute top-0 left-0"
          :class="{
            'progress-bar-created': session.status === 'created' || session.status === 'started' || session.status === 'resumed',
            'progress-bar-processing': session.status === 'processing',
            'progress-bar-waiting': session.status === 'waiting',
            'progress-bar-processed': session.status === 'processed',
            'progress-bar-completed': session.status === 'completed',
            'progress-bar-failed': session.status === 'failed' || session.status === 'error' || session.status === 'cancelled',
            'progress-bar-paused': session.status === 'paused'
          }"
          :style="{ width: `${Math.round((session.processed / session.total) * 100) || 0}%` }"
        ></div>
      </div>
    </div>
  </li>
</template>

<script setup lang="ts">
import { formatDateToLocaleString } from '../../utils/dateUtils';
import type { TicketSession } from '../../types/tickets';
import StatusBadge from '../status/StatusBadge.vue';

defineProps<{
  session: TicketSession;
}>();

defineEmits<{
  (e: 'view-details', sessionId: string): void;
}>();
</script>

<style scoped>
.session-item {
  transition: background-color 0.2s ease-in-out;
}
</style>
