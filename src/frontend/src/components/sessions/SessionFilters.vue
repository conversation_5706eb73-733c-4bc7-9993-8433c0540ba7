<template>
  <div class="flex items-center">
    <div class="relative filter-item" style="width: 260px;">
      <VueDatePicker v-model="date" range multi-calendars auto-apply placeholder="Оберіть діапазон дат"
        :enable-time-picker="false" locale="uk" format="dd/MM/yyyy" class="session-date-picker"
        @update:model-value="handleDateUpdate" @cleared="handleDateClear" />
    </div>

    <div class="relative filter-item" style="width: 200px;">
      <Multiselect v-model="selectedUser" :options="users" :searchable="true" :close-on-select="true"
        :show-labels="false" :allow-empty="true" :preserve-search="true" :internal-search="true"
        placeholder="Усі користувачі" track-by="id" label="username" @select="handleUserSelect"
        @remove="handleUserRemove" @input="handleUserSelect" v-show="filtersLoaded">
        <template #singleLabel="{ option }">
          <span :class="{ 'text-gray-500 dark:text-gray-400': option && option.id === -1 }">
            {{ option ? option.username : 'Усі користувачі' }}
          </span>
        </template>
      </Multiselect>
      <div v-if="!filtersLoaded" class="filter-placeholder"></div>
    </div>
    <div class="relative filter-item" style="width: 200px;">
      <Multiselect v-model="selectedStatusObject" :options="[allStatusesOption, ...ticketStatuses]" :searchable="true"
        :close-on-select="true" :show-labels="false" :allow-empty="true" :preserve-search="true" :internal-search="true"
        placeholder="Усі статуси" track-by="value" label="text" @select="handleStatusSelect"
        @remove="handleStatusRemove" @input="handleStatusSelect" v-show="filtersLoaded">
        <template #singleLabel="{ option }">
          <span :class="{ 'text-gray-500 dark:text-gray-400': option && option.value === '' }">
            {{ option ? option.text : 'Усі статуси' }}
          </span>
        </template>
      </Multiselect>
      <div v-if="!filtersLoaded" class="filter-placeholder"></div>
    </div>
    <button @click="clearAllFilters"
      class="text-sm flex items-center justify-center p-2 w-10 h-10 filter-item border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:border-primary-500 focus:ring-0 transition-colors"
      aria-label="Очистити фільтри" title="Очистити фільтри" style="width: auto; min-height: 40px;">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
    <button @click="refreshData"
      class="text-sm flex items-center justify-center p-2 w-10 h-10 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:border-primary-500 focus:ring-0 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      :disabled="isLoading" aria-label="Оновити" title="Оновити"
      style="width: auto; min-height: 40px; margin-left: 7px;">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
      </svg>
    </button>
  </div>
</template>

<script setup lang="ts">
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import { ref, computed, watch, onMounted } from 'vue';
import Multiselect from 'vue-multiselect';
import type { User } from '../../types/user';
import { authApi } from '../../services/api';

defineProps<{
  isLoading: boolean;
}>();

const emit = defineEmits<{
  (e: 'filter', userId: number, status: string, dateRange?: [Date | null, Date | null]): void;
  (e: 'refresh'): void;
}>();

const users = ref<User[]>([]);
const filtersLoaded = ref(false);
const selectedUserId = ref<number>(-1);
const selectedStatus = ref<string>('');
const selectedUser = ref<User | null>(null);
const selectedStatusObject = ref<{ value: string, text: string } | null>(null);
const usersLastUpdated = ref<number>(0);
const usersUpdateInterval = 1 * 60 * 1000;

type DateModelValue = Date[] | null;
const date = ref<DateModelValue>(null);

const ticketStatuses = computed(() => [
  { value: 'processing', text: 'В процесі' },
  { value: 'completed', text: 'Завершено' },
  { value: 'cancelled', text: 'Скасовано' },
  { value: 'resumed', text: 'Відновлено' },
  { value: 'paused', text: 'Призупинено' },
  { value: 'error', text: 'Помилка' },
  { value: 'failed', text: 'Збій' },
  { value: 'created', text: 'Створено' },
  { value: 'started', text: 'Розпочато' },
  { value: 'processed', text: 'Оброблено' },
  { value: 'waiting', text: 'Очікування' },
]);

const allStatusesOption = computed(() => ({ value: '', text: 'Усі статуси' }));

const loadCachedUsers = (): User[] => {
  try {
    const cachedData = localStorage.getItem('cachedUsers');
    if (cachedData) {
      const { users: cachedUsers, timestamp } = JSON.parse(cachedData);
      usersLastUpdated.value = timestamp;

      const now = Date.now();
      if (now - timestamp < usersUpdateInterval) {
        return cachedUsers;
      }
    }
  } catch (err) {
    console.error('Error loading cached users:', err);
  }
  return [];
};

const cacheUsers = (users: User[]) => {
  try {
    const timestamp = Date.now();
    usersLastUpdated.value = timestamp;
    localStorage.setItem('cachedUsers', JSON.stringify({ users, timestamp }));
  } catch (err) {
    console.error('Error caching users:', err);
  }
};

const loadFilters = () => {
  let userIdFromStorage = -1;
  let statusFromStorage = '';
  let dateFromStorage: DateModelValue = null;

  const savedUserFilter = localStorage.getItem('sessionsUserFilter');
  if (savedUserFilter) {
    const userId = parseInt(savedUserFilter);
    if (!isNaN(userId)) {
      userIdFromStorage = userId;
    }
  }

  const savedStatusFilter = localStorage.getItem('sessionsStatusFilter');
  if (savedStatusFilter) {
    statusFromStorage = savedStatusFilter;
  }

  const savedDateFilter = localStorage.getItem('sessionsDateFilter');
  if (savedDateFilter) {
    try {
      const parsedDates = JSON.parse(savedDateFilter);
      if (Array.isArray(parsedDates) && parsedDates.length === 2) {
        dateFromStorage = parsedDates.map(d => d ? new Date(d) : null).filter(Boolean) as Date[];
        date.value = dateFromStorage;
      }
    } catch (err) {
      console.error('Error parsing saved date filter:', err);
    }
  }

  selectedUserId.value = userIdFromStorage;
  selectedStatus.value = statusFromStorage;

  if (userIdFromStorage === -1) {
    selectedUser.value = getAllUsersOption();
  } else {
    const foundUser = users.value.find(u => u.id === userIdFromStorage);
    selectedUser.value = foundUser || getAllUsersOption();
  }

  if (statusFromStorage === '') {
    selectedStatusObject.value = allStatusesOption.value;
  } else {
    const foundStatus = ticketStatuses.value.find(s => s.value === statusFromStorage);
    selectedStatusObject.value = foundStatus || allStatusesOption.value;
  }

  filtersLoaded.value = true;
  emit('filter', selectedUserId.value, selectedStatus.value, date.value ? [date.value[0], date.value[1]] : undefined);
};

const saveUserFilter = (userId: string | number) => {
  localStorage.setItem('sessionsUserFilter', userId.toString());
};

const saveStatusFilter = (status: string) => {
  localStorage.setItem('sessionsStatusFilter', status);
};

const saveDateFilter = (dateRange: Date[]) => {
  localStorage.setItem('sessionsDateFilter', JSON.stringify(dateRange));
};

const handleUserSelect = (option: User) => {
  if (!option) {
    selectedUserId.value = -1;
    selectedUser.value = getAllUsersOption();
  } else {
    selectedUserId.value = option.id;
    selectedUser.value = option;
  }
  saveUserFilter(selectedUserId.value);
  emit('filter', selectedUserId.value, selectedStatus.value, date.value ? [date.value[0], date.value[1]] : undefined);
};

const handleUserRemove = () => {
  selectedUserId.value = -1;
  selectedUser.value = getAllUsersOption();
  saveUserFilter(-1);
  emit('filter', selectedUserId.value, selectedStatus.value, date.value ? [date.value[0], date.value[1]] : undefined);
};

const handleStatusSelect = (option: { value: string, text: string }) => {
  if (!option) {
    selectedStatus.value = '';
    selectedStatusObject.value = allStatusesOption.value;
  } else {
    selectedStatus.value = option.value;
    selectedStatusObject.value = option;
  }
  saveStatusFilter(selectedStatus.value);
  emit('filter', selectedUserId.value, selectedStatus.value, date.value ? [date.value[0], date.value[1]] : undefined);
};

const handleStatusRemove = () => {
  selectedStatus.value = '';
  selectedStatusObject.value = allStatusesOption.value;
  saveStatusFilter('');
  emit('filter', selectedUserId.value, selectedStatus.value, date.value ? [date.value[0], date.value[1]] : undefined);
};

const handleDateUpdate = (dates: Date[] | null) => {
  if (dates && dates.length === 2) {
    saveDateFilter(dates);
    emit('filter', selectedUserId.value, selectedStatus.value, [dates[0], dates[1]]);
  }
};

const handleDateClear = () => {
  date.value = null;
  localStorage.removeItem('sessionsDateFilter');
  emit('filter', selectedUserId.value, selectedStatus.value, [null, null]);
};

const refreshData = () => {
  fetchUsers(true);
  emit('refresh');
};

const clearAllFilters = () => {
  selectedUserId.value = -1;
  selectedUser.value = getAllUsersOption();
  selectedStatus.value = '';
  selectedStatusObject.value = allStatusesOption.value;
  date.value = null;
  localStorage.removeItem('sessionsUserFilter');
  localStorage.removeItem('sessionsStatusFilter');
  localStorage.removeItem('sessionsDateFilter');
  emit('filter', -1, '', [null, null]);
};

const preloadSelectOptions = () => {
  const isDarkMode = document.documentElement.classList.contains('dark');

  if (isDarkMode) {
    document.documentElement.style.setProperty('--select-bg-color', '#374151');
    document.documentElement.style.setProperty('--select-text-color', 'white');
  } else {
    document.documentElement.style.setProperty('--select-bg-color', 'white');
    document.documentElement.style.setProperty('--select-text-color', '#1f2937');
  }

  document.documentElement.style.setProperty('--select-text-position', '0');
  document.documentElement.style.setProperty('--select-text-padding', '0');
};

watch(() => users.value, (newUsers) => {
  if (newUsers.length > 0) {
    if (selectedUserId.value !== undefined && !selectedUser.value) {
      const foundUser = newUsers.find(u => u.id === selectedUserId.value);
      if (foundUser) {
        selectedUser.value = foundUser;
      } else if (selectedUserId.value === -1) {
        selectedUser.value = newUsers.find(u => u.id === -1) || null;
      }
    } else if (selectedUserId.value !== -1) {
      const foundUser = newUsers.find(u => u.id === selectedUserId.value);
      if (foundUser) {
        selectedUser.value = foundUser;
      }
    }

    emit('filter', selectedUserId.value, selectedStatus.value, date.value ? [date.value[0], date.value[1]] : undefined);
  }
});

watch(() => selectedUserId.value, (newUserId) => {
  if (newUserId === undefined || newUserId === null) {
    selectedUserId.value = -1;
    selectedUser.value = getAllUsersOption();
  } else if (newUserId === -1) {
    selectedUser.value = getAllUsersOption();
  } else if (!selectedUser.value || selectedUser.value.id !== newUserId) {
    const foundUser = users.value.find(u => u.id === newUserId);
    if (foundUser) {
      selectedUser.value = foundUser;
    }
  }
});

watch(() => selectedStatus.value, (newStatus) => {
  if (newStatus === undefined || newStatus === null) {
    selectedStatus.value = '';
    selectedStatusObject.value = allStatusesOption.value;
  } else if (newStatus === '') {
    selectedStatusObject.value = allStatusesOption.value;
  } else if (!selectedStatusObject.value || selectedStatusObject.value.value !== newStatus) {
    const foundStatus = ticketStatuses.value.find(s => s.value === newStatus);
    if (foundStatus) {
      selectedStatusObject.value = foundStatus;
    }
  }
});

const getAllUsersOption = (): User => {
  return { id: -1, username: 'Усі користувачі' } as User;
};

const addAllUsersOption = (usersList: User[]): User[] => {
  return [getAllUsersOption(), ...usersList];
};

async function fetchUsers(forceRefresh = false) {
  try {
    const now = Date.now();
    const shouldUpdate = forceRefresh || now - usersLastUpdated.value > usersUpdateInterval || users.value.length === 0;

    if (shouldUpdate) {
      const serverUsers = await authApi.getUsers();
      users.value = addAllUsersOption(serverUsers);
      cacheUsers(serverUsers);
    }
  } catch (err: any) {
    console.error('Error fetching users:', err);

    if (users.value.length === 0) {
      const cachedUsers = loadCachedUsers();
      if (cachedUsers.length > 0) {
        users.value = addAllUsersOption(cachedUsers);
      }
    }
  }
}

onMounted(async () => {
  preloadSelectOptions();

  try {
    selectedUserId.value = -1;
    selectedUser.value = getAllUsersOption();
    selectedStatus.value = '';
    selectedStatusObject.value = allStatusesOption.value;

    const cachedUsers = loadCachedUsers();
    if (cachedUsers.length > 0) {
      users.value = addAllUsersOption(cachedUsers);
    }

    loadFilters();
    await fetchUsers(true);

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class' &&
          (mutation.target as Element).classList.contains('dark') !== undefined) {
          preloadSelectOptions();
        }
      });
    });

    observer.observe(document.documentElement, { attributes: true });
  } catch (err) {
    console.error('Error during component initialization:', err);
  }
});
</script>

<style scoped>
.filter-item {
  margin-left: 10px;
}

.filter-item:first-child {
  margin-left: 0;
}

.filter-placeholder {
  height: 40px;
  width: 100%;
  background-color: var(--select-bg-color, white);
  border: 1px solid #ccc;
  border-radius: 4px;
}

:deep(.multiselect__tags) {
  height: 40px;
  padding-top: 0;
  padding-bottom: 0;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding-left: 8px;
  border: none !important;
}

:deep(.multiselect__single) {
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
  left: 0;
  margin-left: 0;
  padding-left: 0;
  width: 100%;
}

:deep(.multiselect__placeholder) {
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
  left: 0;
  margin-left: 0;
  padding-left: 0;
  width: 100%;
}

:deep(.multiselect__content-wrapper) {
  cursor: default;
}

:deep(.dark .multiselect__input),
:deep(.dark .multiselect__single) {
  color: white !important;
  background-color: transparent !important;
  position: relative;
  left: 0;
  margin-left: 0;
  padding-left: 0;
}

:deep(.dark .multiselect__content-wrapper) {
  background-color: #374151 !important;
  cursor: default;
}

:deep(.multiselect__placeholder),
:deep(.multiselect__single) {
  text-align: left;
  transform: none !important;
  transition: none !important;
  position: static !important;
  animation: none !important;
  opacity: 1 !important;
}

:deep(.multiselect__select) {
  transition: none !important;
}

:deep(.multiselect--active .multiselect__placeholder) {
  display: none !important;
}

:deep(.multiselect--active .multiselect__single) {
  display: none !important;
}

:deep(.multiselect) {
  font-size: 14px;
  font-weight: normal;
}

:deep(.multiselect__tags) {
  min-height: 40px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

:deep(.multiselect__placeholder),
:deep(.multiselect__single),
:deep(.multiselect__input) {
  margin: 0 !important;
  padding: 0 !important;
  min-height: 0 !important;
  line-height: inherit !important;
  vertical-align: middle !important;
}

:deep(.multiselect__option) {
  padding: 8px 12px;
  line-height: 1.2;
  min-height: 36px;
  display: flex;
  align-items: center;
}

:deep(.dark .multiselect__option) {
  color: white !important;
  cursor: default;
}

:deep(.dark .multiselect__option--selected) {
  background-color: #4B5563 !important;
}

:deep(.session-date-picker .dp__input_wrap) {
  height: 40px;
  min-height: 40px;
  display: flex;
  align-items: center;
}

:deep(.session-date-picker .dp__input) {
  height: 100%;
  padding-left: 8px;
  font-size: 14px;
}

:deep(.session-date-picker .dp__input_icon) {
  top: 50%;
  transform: translateY(-50%);
}

:deep(.dark .session-date-picker .dp__input_wrap) {
  background-color: #374151 !important;
  border-color: #4B5563 !important;
}

:deep(.dark .session-date-picker .dp__input_wrap:hover) {
  background-color: #4B5563 !important;
}

:deep(.dark .session-date-picker .dp__input) {
  color: white !important;
}
</style>
