<template>
  <div class="mt-4 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
    <div v-if="isLoading && initialLoading && !sessions.length" class="border-t border-gray-200 dark:border-gray-700">
      <SessionsSkeletonLoader />
    </div>

    <div v-else-if="error" class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
      <SessionsErrorState :error="error" @retry="$emit('refresh')" />
    </div>

    <div v-else-if="!sessions.length && !isLoading"
      class="px-4 py-5 sm:px-6">
      <SessionsEmptyState />
    </div>

    <div v-else class="sessions-container">
      <ul class="divide-y divide-gray-200 dark:divide-gray-700">
        <SessionsListItem v-for="session in sessions" :key="session.sessionId" :session="session"
          @view-details="(sessionId) => $emit('view-details', sessionId)" />
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue';
import type { TicketSession } from '../../types/tickets';
import SessionsListItem from './SessionsListItem.vue';
import SessionsSkeletonLoader from './SessionsSkeletonLoader.vue';
import SessionsErrorState from './SessionsErrorState.vue';
import SessionsEmptyState from './SessionsEmptyState.vue';

const props = defineProps<{
  sessions: TicketSession[];
  isLoading: boolean;
  initialLoading: boolean;
  error: string | null;
  hasMoreToLoad?: boolean;
}>();

const emit = defineEmits<{
  (e: 'view-details', sessionId: string): void;
  (e: 'refresh'): void;
  (e: 'load-more'): void;
}>();

let scrollTimeout: number | null = null;

const handleScroll = () => {
  if (scrollTimeout !== null) {
    window.clearTimeout(scrollTimeout);
  }

  scrollTimeout = window.setTimeout(() => {
    if (!props.hasMoreToLoad) return;

    const scrollPosition = window.scrollY + window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;

    if (documentHeight - scrollPosition < 200) {
      emit('load-more');
    }
  }, 100);
};

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
  if (scrollTimeout !== null) {
    window.clearTimeout(scrollTimeout);
  }
});
</script>

<style scoped>
.sessions-container {
  width: 100%;
}
</style>
