<template>
  <ul class="divide-y divide-gray-200 dark:divide-gray-700">
    <li v-for="i in 5" :key="i" class="px-4 py-4 sm:px-6 relative session-item animate-pulse">
      <div class="flex items-center justify-between">
        <div class="w-full max-w-[70%]">
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-40 mb-2"></div>
          <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-2"></div>
          <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
        </div>
        <div>
          <div class="h-5 bg-gray-200 dark:bg-gray-700 rounded-full w-20"></div>
        </div>
      </div>
      <div class="mt-2">
        <div class="flex justify-between mb-1">
          <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
          <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-8"></div>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 relative z-10 overflow-hidden shadow-sm">
          <div class="h-1.5 rounded-full absolute top-0 left-0 bg-gray-300 dark:bg-gray-600" style="width: 30%"></div>
        </div>
      </div>
    </li>
  </ul>
</template>

<script setup lang="ts">
</script>

<style scoped>
.session-item {
  transition: background-color 0.2s ease-in-out;
}
</style>
