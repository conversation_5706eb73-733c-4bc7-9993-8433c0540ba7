<template>
  <div class="max-w-md mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-6 text-gray-800 dark:text-white">Скидання паролю</h2>

    <form @submit.prevent="handleSubmit" class="space-y-4">
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Email
        </label>
          <input placeholder="<EMAIL>" id="email" v-model="email" type="email" required class="input w-full" />
      </div>

      <div v-if="error"
        class="p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-100 rounded-md flex items-start">
        <svg class="w-5 h-5 mr-2 flex-shrink-0 text-red-500 dark:text-red-300" fill="none" stroke="currentColor"
          viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>{{ error }}</span>
      </div>

      <div v-if="success"
        class="p-3 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-100 rounded-md flex items-start">
        <svg class="w-5 h-5 mr-2 flex-shrink-0 text-green-500 dark:text-green-300" fill="none" stroke="currentColor"
          viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>{{ success }}</span>
      </div>

      <div>
        <button type="submit" :disabled="isLoading" class="btn btn-primary w-full flex justify-center">
          <span v-if="isLoading" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
            Обробка...
          </span>
          <span v-else>Надіслати інструкції</span>
        </button>
      </div>

      <div class="mt-4">
        <router-link to="/login" class="btn btn-secondary w-full flex justify-center">
          Повернутися до входу
        </router-link>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from '../../stores/auth';

const authStore = useAuthStore();

const email = ref('');
const error = ref('');
const success = ref('');
const isLoading = ref(false);

async function handleSubmit() {
  error.value = '';
  success.value = '';

  isLoading.value = true;

  try {
    const result = await authStore.requestPasswordReset(email.value);

    if (result) {
      success.value = 'Якщо обліковий запис з цією електронною адресою існує, інструкції щодо скидання паролю будуть надіслані на вказану адресу';
      email.value = '';
    } else {
      error.value = authStore.error || 'Помилка при запиті на скидання паролю';
    }
  } catch (err) {
    error.value = 'Помилка при запиті на скидання паролю';
    console.error('Request password reset error:', err);
  } finally {
    isLoading.value = false;
  }
}
</script>
