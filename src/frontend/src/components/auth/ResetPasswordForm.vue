<template>
  <div class="max-w-md mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-6 text-gray-800 dark:text-white">Встановлення нового паролю</h2>

    <form @submit.prevent="handleSubmit" class="space-y-4">
      <div>
        <label for="newPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Новий пароль
        </label>
        <div class="relative">
          <input id="newPassword" v-model="newPassword" :type="showNewPassword ? 'text' : 'password'" required
            class="input w-full "
            placeholder="Введіть новий пароль" />
          <button type="button"
            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
            @click="showNewPassword = !showNewPassword">
            <svg v-if="showNewPassword" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
              </path>
            </svg>
            <svg v-else class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21">
              </path>
            </svg>
          </button>
        </div>
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
          Пароль повинен містити щонайменше 8 символів
        </p>
      </div>

      <div>
        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Підтвердження нового паролю
        </label>
        <div class="relative">
          <input id="confirmPassword" v-model="confirmPassword" :type="showConfirmPassword ? 'text' : 'password'"
            required
            class="input w-full"
            placeholder="Підтвердіть новий пароль" />
          <button type="button"
            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
            @click="showConfirmPassword = !showConfirmPassword">
            <svg v-if="showConfirmPassword" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
              </path>
            </svg>
            <svg v-else class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21">
              </path>
            </svg>
          </button>
        </div>
      </div>

      <div v-if="error"
        class="p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-100 rounded-md flex items-start">
        <svg class="w-5 h-5 mr-2 flex-shrink-0 text-red-500 dark:text-red-300" fill="none" stroke="currentColor"
          viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>{{ error }}</span>
      </div>

      <div v-if="success"
        class="p-3 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-100 rounded-md flex items-start">
        <svg class="w-5 h-5 mr-2 flex-shrink-0 text-green-500 dark:text-green-300" fill="none" stroke="currentColor"
          viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>{{ success }}</span>
      </div>

      <div>
        <button type="submit" :disabled="isLoading" class="btn btn-primary w-full flex justify-center">
          <span v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
            Обробка...
          </span>
          <span v-else>Встановити новий пароль</span>
        </button>
      </div>

      <div v-if="success" class="mt-4">
        <router-link to="/login" class="btn btn-secondary w-full flex justify-center">
          Перейти до входу
        </router-link>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useAuthStore } from '../../stores/auth';

const route = useRoute();
const authStore = useAuthStore();

const token = ref('');
const newPassword = ref('');
const confirmPassword = ref('');
const error = ref('');
const success = ref('');
const isLoading = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

onMounted(() => {
  token.value = route.query.token as string || '';

  if (!token.value) {
    error.value = 'Недійсний або відсутній токен скидання паролю';
  }
});

async function handleSubmit() {
  error.value = '';
  success.value = '';

  if (!token.value) {
    error.value = 'Недійсний або відсутній токен скидання паролю';
    return;
  }

  if (newPassword.value.length < 8) {
    error.value = 'Новий пароль повинен містити щонайменше 8 символів';
    return;
  }

  if (newPassword.value !== confirmPassword.value) {
    error.value = 'Паролі не співпадають';
    return;
  }

  isLoading.value = true;

  try {
    const result = await authStore.resetPassword(token.value, newPassword.value);

    if (result) {
      success.value = 'Пароль успішно скинуто. Тепер ви можете увійти з новим паролем.';
      newPassword.value = '';
      confirmPassword.value = '';
    } else {
      error.value = authStore.error || 'Помилка при скиданні паролю';
    }
  } catch (err) {
    error.value = 'Помилка при скиданні паролю';
    console.error('Reset password error:', err);
  } finally {
    isLoading.value = false;
  }
}
</script>
