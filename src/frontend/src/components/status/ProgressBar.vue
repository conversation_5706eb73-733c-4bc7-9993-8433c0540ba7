<template>
  <div class="mb-4">
    <div class="flex justify-between mb-1">
      <span class="text-base font-medium text-gray-700 dark:text-gray-300">
        {{ displayProcessed }} / {{ displayTotal }}
      </span>
      <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
        {{ displayPercentage }}%
      </span>
    </div>
    <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
      <div class="bg-primary-600 h-2.5 rounded-full transition-all duration-300 ease-out"
        :style="{ width: `${displayPercentage}%` }"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';

const props = defineProps<{
  processed: number;
  total: number;
}>();


const displayProcessed = ref(props.processed);
const displayTotal = ref(props.total);


const progressPercentage = computed(() => {

  if (displayTotal.value <= 0) return 0;
  return Math.round((displayProcessed.value / displayTotal.value) * 100) || 0;
});


const displayPercentage = ref(progressPercentage.value);


watch(() => props.processed, (newValue) => {
  if (newValue > 0) {
    displayProcessed.value = newValue;
  }
}, { immediate: true });

watch(() => props.total, (newValue) => {
  if (newValue > 0) {
    displayTotal.value = newValue;
  }
}, { immediate: true });


watch(progressPercentage, (newValue) => {
  displayPercentage.value = newValue;
}, { immediate: true });


onMounted(() => {

  if (props.total > 0) {
    displayTotal.value = props.total;
  }

  if (props.processed >= 0) {
    displayProcessed.value = props.processed;
  }


  displayPercentage.value = progressPercentage.value;
});
</script>
