<template>
  <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <div class="flex items-center gap-3">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
          Створення тікетів
        </h3>
        <StatusBadge :status="session.status" />

        <span class="hidden">{{ logStatus(session.status) }}</span>
      </div>
      <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
        ID: <span class="cursor-pointer hover:underline text-primary-600 dark:text-primary-400"
          @click="emit('viewDetails', session.sessionId)">{{ session.sessionId }}</span>
      </p>
      <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
        ℹ️ Можна поки піти попити чаю, а тікети створяться самі. Сторінку можна закривати, якщо вона вам заважає.
        Повернутись до споглядання процесу створення тікетів можна будь-коли.
      </p>
    </div>

    <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
      <ProgressBar :processed="session.processed" :total="session.total" />

      <div class="flex items-center justify-end">
        <ActionButtons :session="session" :is-process-completed="isProcessCompleted" :can-be-resumed="canBeResumed"
          :has-resumed-after-last-pause="hasResumedAfterLastPause" :is-pause-loading="isPauseLoading"
          :is-resume-loading="isResumeLoading" @reset="emit('reset')" @pause="handlePause(session.sessionId)"
          @resume="handleResume(session.sessionId)" @cancel="emit('showCancelDialog')" />
      </div>
    </div>

    <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
      <EventsList :events="session.events" :showSearch="false" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { TicketSession } from '../../types/tickets';
import StatusBadge from './StatusBadge.vue';
import ProgressBar from './ProgressBar.vue';
import EventsList from './EventsList.vue';
import ActionButtons from '../tickets/ActionButtons.vue';

const props = defineProps<{
  session: TicketSession;
}>();

const isPauseLoading = ref(false);
const isResumeLoading = ref(false);


function logStatus(status: string) {
  console.log(`SessionProgress rendering status: ${status}`);
  return '';
}

const emit = defineEmits<{
  (e: 'reset'): void;
  (e: 'pause', sessionId: string): void;
  (e: 'resume', sessionId: string): void;
  (e: 'showCancelDialog'): void;
  (e: 'viewDetails', sessionId: string): void;
}>();

const handlePause = (sessionId: string) => {
  isPauseLoading.value = true;
  try {
    emit('pause', sessionId);
  } finally {
    setTimeout(() => {
      isPauseLoading.value = false;
    }, 300);
  }
};

const handleResume = (sessionId: string) => {
  isResumeLoading.value = true;
  try {
    emit('resume', sessionId);
  } finally {
    setTimeout(() => {
      isResumeLoading.value = false;
    }, 300);
  }
};

const isProcessCompleted = computed(() => {
  const hasCompletedStatus = ['completed', 'cancelled', 'failed'].includes(props.session.status);

  const hasCompletionEvent = props.session.events?.some(e =>
    ['completed', 'cancelled', 'failed'].includes(e.status)
  );

  const allItemsProcessed = props.session.processed === props.session.total && props.session.total > 0;

  if (hasCompletedStatus || hasCompletionEvent || allItemsProcessed) {
    console.log(`Process completion detected: status=${props.session.status}, hasCompletionEvent=${hasCompletionEvent}, allItemsProcessed=${allItemsProcessed}`);
  }

  return hasCompletedStatus || hasCompletionEvent || allItemsProcessed;
});

const hasResumedAfterLastPause = computed(() => {
  if (!props.session.events) return false;

  const events = props.session.events;
  const pauseEvents = events.filter(e => e.status === 'paused');
  const resumeEvents = events.filter(e => e.status === 'resumed');

  if (pauseEvents.length === 0) return false;
  if (resumeEvents.length === 0) return false;

  const lastPauseEvent = pauseEvents[pauseEvents.length - 1];
  const lastResumeEvent = resumeEvents[resumeEvents.length - 1];

  if (!lastPauseEvent.timestamp || !lastResumeEvent.timestamp) return false;

  return new Date(lastResumeEvent.timestamp) > new Date(lastPauseEvent.timestamp);
});

const canBeResumed = computed(() => {
  const isPaused = (props.session.status === 'paused' && props.session.paused !== false) || props.session.paused === true;
  const hasPauseEvent = props.session.events?.some(e => e.status === 'paused');
  const isProcessActive = ['processing', 'resumed'].includes(props.session.status) && props.session.paused !== true;
  const isResumed = (props.session.status === 'resumed' || props.session.status === 'processing') && props.session.paused !== true;

  console.log(`canBeResumed check: status=${props.session.status}, paused=${props.session.paused}, isPaused=${isPaused}, hasPauseEvent=${hasPauseEvent}, hasResumedAfterLastPause=${hasResumedAfterLastPause.value}, isProcessActive=${isProcessActive}, isResumed=${isResumed}`);


  console.log(`Raw session state in SessionProgress: status=${props.session.status}, paused=${props.session.paused}`);

  return (isPaused || (hasPauseEvent && !hasResumedAfterLastPause.value)) &&
    !isProcessCompleted.value &&
    !isProcessActive &&
    !isResumed;
});


watch(() => [props.session.status, props.session.paused], ([status, paused]) => {
  console.log(`Session state changed in SessionProgress: status=${status}, paused=${paused}`);


  console.log(`Session details in SessionProgress:`, {
    sessionId: props.session.sessionId,
    status,
    paused,
    processed: props.session.processed,
    total: props.session.total,
    hasResumeEvents: props.session.events?.some(e => e.status === 'resumed') || false,
    hasPauseEvents: props.session.events?.some(e => e.status === 'paused') || false
  });
}, { immediate: true });


</script>
