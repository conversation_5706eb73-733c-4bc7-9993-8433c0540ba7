<template>
  <span class="status-badge" :class="{
    'status-badge-created': status === 'created' || status === 'started' || status === 'resumed',
    'status-badge-processing': status === 'processing',
    'status-badge-waiting': status === 'waiting',
    'status-badge-processed': status === 'processed',
    'status-badge-completed': status === 'completed',
    'status-badge-failed': status === 'failed' || status === 'error' || status === 'cancelled',
    'status-badge-paused': status === 'paused'
  }">
    {{ statusText }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  status: string;
  inEventsList?: boolean;
}>();

const statusText = computed(() => {
  switch (props.status) {
    case 'created':
      return 'Створено';
    case 'started':
      return 'Розпочато';
    case 'resumed':
      return props.inEventsList ? 'В процесі' : 'Відновлено';
    case 'processing':
      return 'В процесі';
    case 'processed':
      return 'Оброблено';
    case 'waiting':
      return 'Очікування';
    case 'completed':
      return 'Завершено';
    case 'failed':
    case 'error':
      return 'Помилка';
    case 'cancelled':
      return 'Скасовано';
    case 'paused':
      return 'Призупинено';
    default:
      return 'Невідомо';
  }
});
</script>
