<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h4 class="text-md font-medium text-gray-900 dark:text-white">Останн<PERSON> події</h4>
    </div>

    <div v-if="showSearch" class="relative mb-2">
      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
        </svg>
      </div>
      <input
        type="text"
        v-model="searchQuery"
        class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-0 focus:border-gray-400 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:border-gray-500"
        placeholder="Пошук по подіям..."
      />
      <button
        v-if="searchQuery"
        @click="clearSearch"
        class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
      >
        <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <div class="max-h-60 overflow-y-auto">
      <div
        v-for="(event, index) in filteredEvents"
        :key="index"
        class="mb-2 p-2 border-l-4 rounded-r-md"
        :class="{
          'event-border-created': event.status === 'created' || event.status === 'started' || event.status === 'resumed',
          'event-border-processing': event.status === 'processing',
          'event-border-waiting': event.status === 'waiting',
          'event-border-processed': event.status === 'processed',
          'event-border-completed': event.status === 'completed',
          'event-border-failed': event.status === 'failed' || event.status === 'error' || event.status === 'cancelled',
          'event-border-paused': event.status === 'paused'
        }"
      >
        <div class="flex items-center">
          <StatusBadge :status="event.status" :inEventsList="true" class="mr-2" />
          <p class="text-sm text-gray-700 dark:text-gray-300">{{ translateEventMessage(event.message) }}</p>
        </div>
        <div class="flex justify-between mt-1">
          <p v-if="event.timestamp" class="text-xs text-gray-500 dark:text-gray-400">
            {{ formatLogsDate(event.timestamp) }}
          </p>
          <!-- <p v-if="typeof event.processed === 'number' && typeof event.total === 'number'" class="text-xs text-gray-500 dark:text-gray-400">
            {{ event.processed }}/{{ event.total }}
          </p> -->
        </div>
      </div>

      <div v-if="reversedEvents.length === 0" class="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
        Очікування подій...
      </div>

      <div v-if="!filteredEvents.length && searchQuery && reversedEvents.length > 0" class="p-4 text-center text-sm text-gray-500 dark:text-gray-400">
        Нічого не знайдено за запитом "{{ searchQuery }}"
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { TicketEvent } from '../../types/tickets';
import StatusBadge from './StatusBadge.vue';
import { formatDateToLocaleString, formatLogsDate } from '../../utils/dateUtils';
import { translateEventMessage } from '../../utils/messageTranslations';

const props = defineProps<{
  events: TicketEvent[];
  showSearch?: boolean;
}>();

const { showSearch = false } = props;

const searchQuery = ref('');

const reversedEvents = computed(() => {
  if (!props.events || props.events.length === 0) return [];

  console.log('Raw events:', JSON.stringify(props.events));

  const allEvents = [...props.events];

  const eventsWithTimestamps = allEvents.map(event => {
    if (!event.timestamp) {
      console.warn('Event without timestamp:', event);
      return { ...event, timestamp: new Date().toISOString() };
    }
    return event;
  });

  const sortedEvents = eventsWithTimestamps.sort((a, b) => {
    const timeA = new Date(a.timestamp || 0).getTime();
    const timeB = new Date(b.timestamp || 0).getTime();
    return timeB - timeA;
  });

  console.log('Sorted events (newest first):', sortedEvents.map(e => `${e.status} - ${e.message} - ${e.timestamp}`));

  const uniqueEvents: TicketEvent[] = [];
  const seenKeys = new Map<string, string>();

  const hasPauseEvent = sortedEvents.some(e => e.status === 'paused');
  const hasResumeEvent = sortedEvents.some(e => e.status === 'resumed');

  console.log(`Events check - hasPauseEvent: ${hasPauseEvent}, hasResumeEvent: ${hasResumeEvent}`);

  for (const event of sortedEvents) {
    const key = `${event.status}-${event.message || ''}-${event.processed || 0}`;

    if (!seenKeys.has(key)) {
      seenKeys.set(key, event.timestamp || '');
      uniqueEvents.push(event);
    } else {
      const existingTimestamp = seenKeys.get(key) || '';
      const currentTimestamp = event.timestamp || '';

      if (new Date(currentTimestamp).getTime() > new Date(existingTimestamp).getTime()) {
        const existingIndex = uniqueEvents.findIndex(e =>
          e.status === event.status &&
          e.message === event.message &&
          e.processed === event.processed
        );

        if (existingIndex !== -1) {
          uniqueEvents[existingIndex] = event;
          seenKeys.set(key, currentTimestamp);
        }
      }
    }
  }

  uniqueEvents.sort((a, b) => {
    const timeA = new Date(a.timestamp || 0).getTime();
    const timeB = new Date(b.timestamp || 0).getTime();
    return timeB - timeA;
  });

  console.log('Final unique events:', uniqueEvents.map(e => `${e.status} - ${e.message} - ${e.timestamp}`));

  return uniqueEvents.slice(0, 20);
});

const filteredEvents = computed(() => {
  if (!searchQuery.value.trim()) {
    return reversedEvents.value;
  }

  const query = searchQuery.value.toLowerCase().trim();

  return reversedEvents.value.filter(event => {
    const translatedMessage = translateEventMessage(event.message).toLowerCase();
    const originalMessage = event.message.toLowerCase();
    const dateString = event.timestamp ? formatDateToLocaleString(event.timestamp).toLowerCase() : '';
    const status = event.status.toLowerCase();

    return translatedMessage.includes(query) ||
           originalMessage.includes(query) ||
           dateString.includes(query) ||
           status.includes(query) ||
           (event.current && event.current.toLowerCase().includes(query));
  });
});

function clearSearch() {
  searchQuery.value = '';
}
</script>
