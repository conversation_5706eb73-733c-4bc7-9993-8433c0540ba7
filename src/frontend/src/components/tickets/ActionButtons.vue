<template>
  <div class="flex space-x-4">
    <PauseResumeButton :session="session" :is-process-completed="isProcessCompleted" :can-be-resumed="canBeResumed"
      :has-resumed-after-last-pause="hasResumedAfterLastPause" :is-pause-loading="isPauseLoading"
      :is-resume-loading="isResumeLoading" @pause="$emit('pause', session.sessionId)"
      @resume="$emit('resume', session.sessionId)" />

    <button v-if="!isProcessCompleted && !['completed', 'cancelled', 'failed'].includes(session.status)"
      @click="$emit('cancel')" title="Скасувати" class="btn btn-danger text-sm w-12 flex items-center justify-center"
      :disabled="isLoading">
      <div class="flex items-center justify-center">
        <span v-if="isCancelLoading" class="mr-2">
          <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
          </svg>
        </span>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor"
          aria-hidden="true">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
            clip-rule="evenodd" />
        </svg>
        <span class="sr-only">Скасувати</span>
      </div>
    </button>

    <button v-if="isProcessCompleted" @click="$emit('reset')" title="Нова сесія"
      class="btn btn-primary text-sm w-32 flex items-center justify-center">
      <span>Нова сесія</span>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { TicketSession } from '../../types/tickets'
import { useTicketsStore } from '../../stores/tickets'
import PauseResumeButton from './PauseResumeButton.vue'

const ticketsStore = useTicketsStore()
const isCancelLoading = ref(false)

defineProps<{
  session: TicketSession;
  isProcessCompleted: boolean;
  canBeResumed: boolean;
  hasResumedAfterLastPause: boolean;
  isPauseLoading?: boolean;
  isResumeLoading?: boolean;
}>();

defineEmits<{
  (e: 'pause', sessionId: string): void;
  (e: 'resume', sessionId: string): void;
  (e: 'cancel'): void;
  (e: 'reset'): void;
}>();

const isLoading = computed(() => {
  return ticketsStore.isLoading || isCancelLoading.value;
});
</script>
