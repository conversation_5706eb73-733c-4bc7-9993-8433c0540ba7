<template>
  <div class="relative w-12 h-10">
    <div :class="{ 'use-transition': allowButtonAnimation }">
      <button
        v-if="!isProcessCompleted &&
  (!['completed', 'cancelled', 'failed'].includes(session.status)) &&
              (session.status === 'processing' || session.status === 'resumed' || session.paused === false)"
        key="pause"
        @click="pauseProcess"
        title="Пауза"
        class="btn btn-purple text-sm absolute inset-0 w-full flex items-center justify-center button-transition"
        :disabled="isPauseLoading || isResumeLoading || isLoading"
      >
        <div class="flex items-center justify-center">
          <span v-if="isPauseLoading" class="mr-2">
            <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm4 0a1 1 0 112 0v4a1 1 0 11-2 0V8z" clip-rule="evenodd" />
          </svg>
          <span class="sr-only">Пауза</span>
        </div>
      </button>

      <button
        v-else-if="!isProcessCompleted && ((session.status === 'paused' && session.paused !== false) || session.paused === true)"
        key="resume"
        @click="resumeProcess"
        title="Продовжити"
        class="btn btn-success text-sm absolute inset-0 w-full flex items-center justify-center button-transition"
        :disabled="isPauseLoading || isResumeLoading || isLoading"
      >
        <div class="flex items-center justify-center">
          <span v-if="isResumeLoading" class="mr-2">
            <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
          </svg>
          <span class="sr-only">Продовжити</span>
        </div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import type { TicketSession } from '../../types/tickets'
import { useTicketsStore } from '../../stores/tickets'

const ticketsStore = useTicketsStore()
const localIsPauseLoading = ref(false)
const localIsResumeLoading = ref(false)
const allowButtonAnimation = ref(false)

const isPauseLoading = computed(() => props.isPauseLoading !== undefined ? props.isPauseLoading : localIsPauseLoading.value)
const isResumeLoading = computed(() => props.isResumeLoading !== undefined ? props.isResumeLoading : localIsResumeLoading.value)

const props = defineProps<{
  session: TicketSession
  isProcessCompleted: boolean
  canBeResumed: boolean
  hasResumedAfterLastPause: boolean
  isPauseLoading?: boolean
  isResumeLoading?: boolean
}>()

const emit = defineEmits<{
  (e: 'pause', sessionId: string): void
  (e: 'resume', sessionId: string): void
}>()

const isLoading = computed(() => {
  return ticketsStore.isLoading
})

const debugButtonConditions = computed(() => {
  return {
    showPauseButton: !props.isProcessCompleted &&
      (!['completed', 'cancelled', 'failed'].includes(props.session.status)) &&
                     (props.session.status === 'processing' || props.session.status === 'resumed' || props.session.paused === false),
    showResumeButton: !props.isProcessCompleted && ((props.session.status === 'paused' && props.session.paused !== false) || props.session.paused === true),
    sessionStatus: props.session.status,
    sessionPaused: props.session.paused,
    isProcessCompleted: props.isProcessCompleted,
    canBeResumed: props.canBeResumed,
    hasResumedAfterLastPause: props.hasResumedAfterLastPause
  }
})

watch(() => debugButtonConditions.value, (newVal) => {
  console.log('Button visibility conditions:', newVal)

  console.log(`Raw session state: status=${props.session.status}, paused=${props.session.paused}`)
}, { immediate: true })

watch(() => [props.session.status, props.session.paused], ([status, paused]) => {
  console.log(`Session state changed: status=${status}, paused=${paused}`)
}, { immediate: true })

async function pauseProcess() {
  if (props.isPauseLoading === undefined) {
    localIsPauseLoading.value = true
  }
  try {
    emit('pause', props.session.sessionId)
  } finally {
    if (props.isPauseLoading === undefined) {
      setTimeout(() => {
        localIsPauseLoading.value = false
      }, 300)
    }
  }
}

async function resumeProcess() {
  if (props.isResumeLoading === undefined) {
    localIsResumeLoading.value = true
  }
  try {
    emit('resume', props.session.sessionId)
  } finally {
    if (props.isResumeLoading === undefined) {
      setTimeout(() => {
        localIsResumeLoading.value = false
      }, 300)
    }
  }
}

onMounted(() => {
  setTimeout(() => {
    allowButtonAnimation.value = true
  }, 100)
})
</script>

<style scoped>
.button-transition {
  opacity: 1;
  transform: scale(1);
  transition: none;
}

.use-transition .button-transition {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.button-transition[style*="display: none"] {
  opacity: 0;
  transform: scale(0.95);
}
</style>
