<template>
  <FormField
    id="subscriptionId"
    label="SubscriptionId"
    :model-value="modelValue"
    :error="error"
    :rows="2"
    placeholder="1234567
1234568"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <template v-slot:counter>
      <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
        Кількість: {{ lineCount }}
      </div>
    </template>
  </FormField>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import FormField from './FormField.vue';

const props = defineProps<{
  modelValue: string;
  error?: string;
}>();

defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const lineCount = computed(() => {
  if (!props.modelValue) return 0;
  return props.modelValue
    .split(/\n|\r\n?/)
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .length;
});
</script>
