<template>
  <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
    <transition name="fade">
      <div v-if="showWarning"
        class="relative bg-amber-50 dark:bg-amber-900/40 border border-amber-400 dark:border-amber-600 rounded-lg mb-4 transition-all duration-300">

        <div class="px-4 py-5 sm:px-6">
          <button @click="dismissWarning"
            class="absolute top-2 right-2 text-red-500 hover:text-red-700 dark:text-red-500 dark:hover:text-red-700 transition-colors duration-200 rounded-full p-1 focus:outline-none focus:ring-2 focus:ring-red-400 dark:focus:ring-red-600">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"
              aria-hidden="true">
              <path fill-rule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clip-rule="evenodd" />
            </svg>
            <span class="sr-only">Закрити</span>
          </button>

          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white flex items-center">
            <svg class="h-6 w-6 mr-2 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <span class="font-bold">З великою силою приходить і велика відповідальність</span>
          </h3>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
            Перед використанням обов'язково ознайомтеся з <a :href="docsFullUrl" target="_blank"
              class="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:underline inline-flex items-center font-medium">документацією
              <svg class="ml-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                  d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z" />
                <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z" />
              </svg>
            </a><br>
            Уважно заповнюйте всі поля для масового створення тікетів. Деякі з них, наприклад <i>Title</i>, можуть містити довільні значення, тому важливо бути уважними — існує ризик створити кейс із заголовком на кшталт "<b>Хехе Прикол</b>". Натомість інші поля, такі як <i>CategoryName</i>, не допускають імпровізації. Тож будьте уважними та відповідальними.
          </p>
        </div>
      </div>
    </transition>

    <div>
      <form @submit.prevent="submitForm">
        <div class="px-4 py-5 sm:p-6">
          <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            <div class="sm:col-span-3">
              <MSISDNField :model-value="formData.MSISDN" :error="msisdnError"
                @update:model-value="(value) => { formData.MSISDN = value; clearMsisdnError(); }" />
            </div>

            <div class="sm:col-span-3">
              <SubscriptionIdField :model-value="formData.SubscriptionId ?? ''" :error="subscriptionIdError"
                @update:model-value="(value) => { formData.SubscriptionId = value; clearSubscriptionIdError(); }" />
            </div>

            <div class="sm:col-span-3">
              <FormField id="categoryName" label="CategoryName" :model-value="formData.CategoryName"
                @update:model-value="formData.CategoryName = $event" required />
            </div>

            <div class="sm:col-span-3">
              <FormField id="virtualTeamName" label="VirtualTeamName" :model-value="formData.VirtualTeamName"
                @update:model-value="formData.VirtualTeamName = $event" required />
            </div>

            <div class="sm:col-span-3">
              <FormField id="reactionName" label="ReactionName" :model-value="formData.ReactionName"
                @update:model-value="formData.ReactionName = $event" required />
            </div>

            <div class="sm:col-span-3">
              <FormField id="title" label="Title" :model-value="formData.Title"
                @update:model-value="formData.Title = $event" required />
            </div>

            <div class="sm:col-span-3">
              <label for="TickerStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                TickerStatus
              </label>
              <div class="mt-1">
                <select id="TickerStatus" v-model="formData.TickerStatus" required
                  class="input w-full bg-white cursor-pointer">
                  <option value="1">Активний</option>
                  <option value="2">Закритий</option>
                  <option value="3">Відкладений</option>
                  <option value="4">Видалений</option>
                  <option value="5">Складений</option>
                </select>
              </div>
            </div>

            <div class="sm:col-span-3">
              <label for="NotificationType" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                NotificationType
              </label>
              <div class="mt-1">
                <select id="NotificationType" v-model="formData.NotificationType" required
                  class="input w-full bg-white cursor-pointer">
                  <option value="1">Email</option>
                  <option value="2">SMS</option>
                  <option value="3">None</option>
                  <option value="4">Callback</option>
                </select>
              </div>
            </div>

            <div class="sm:col-span-3">
              <label for="InformSubscriber" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                InformSubscriber
              </label>
              <div class="mt-1">
                <select id="InformSubscriber" v-model="formData.InformSubscriber" required
                  class="input w-full bg-white cursor-pointer">
                  <option value="4">Не потрібно</option>
                  <option value="2">За рішенням - SMS, про продовження не інформувати</option>
                  <option value="5">За допомогою email</option>
                  <option value="3">За допомогою SMS</option>
                  <option value="1">За допомогою телефонного дзвінка</option>
                </select>
              </div>
            </div>

            <div class="sm:col-span-3">
              <label for="ResolutionReason" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                ResolutionReason
              </label>
              <div class="mt-1">
                <select id="ResolutionReason" v-model="formData.ResolutionReason" required
                  class="input w-full bg-white cursor-pointer">
                  <option value="">Оберіть значення</option>
                  <option value="6">Проінформовано SMS</option>
                  <option value="4">Проінформовано дзвінком</option>
                  <option value="19">Інформування не було потрібне</option>
                </select>
              </div>
            </div>

            <div class="sm:col-span-3">
              <FormField id="complainDetails" label="ComplainDetails" :model-value="formData.ComplainDetails"
                @update:model-value="formData.ComplainDetails = $event" :rows="2" required />
            </div>

            <div class="sm:col-span-3">
              <FormField id="resolutionSummary" label="ResolutionSummary" :model-value="formData.ResolutionSummary"
                @update:model-value="formData.ResolutionSummary = $event" :rows="2" required />
            </div>
          </div>

          <div v-if="error" class="mt-4 text-sm text-red-600 dark:text-red-400">
            {{ error }}
          </div>
        </div>

        <div class="px-4 py-3 bg-gray-50 dark:bg-[#252d39] flex justify-center space-x-4 sm:px-6">
          <button type="button" title="Відновити стандартні значення полів"
            class="btn btn-secondary flex items-center justify-center border border-gray-300 dark:border-transparent"
            @click="$emit('showResetDialog')" :disabled="isLoading">
            <div class="flex items-center justify-center">
              <span>Скинути</span>
            </div>
          </button>
          <button type="submit" class="btn btn-primary flex items-center justify-center" :disabled="isLoading">
            <div class="flex items-center justify-center">
              <span v-if="isLoading" class="mr-2">
                <svg class="animate-spin h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
              </span>
              <span>Старт</span>
            </div>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { TicketFormData } from '../../types/tickets';
import FormField from './FormField.vue';
import MSISDNField from './MSISDNField.vue';
import SubscriptionIdField from './SubscriptionIdField.vue';

const mode = import.meta.env.MODE;
const docsUrl = mode === 'production'
  ? import.meta.env.VITE_PROD_URL || 'https://cca.kyivstar.ua'
  : import.meta.env.VITE_DEV_DOCS_URL || 'http://localhost:5174';
const docsFullUrl = `${docsUrl}/cm-echo/docs/`;

const msisdnError = ref('');
const subscriptionIdError = ref('');

const isDismissed = localStorage.getItem('cm-echo-warning-dismissed') === 'true';
const showWarning = ref(!isDismissed);

function dismissWarning() {
  showWarning.value = false;
  localStorage.setItem('cm-echo-warning-dismissed', 'true');
}

const props = defineProps<{
  formData: TicketFormData;
  isLoading: boolean;
  error: string | null;
}>();

const emit = defineEmits<{
  (e: 'update:formData', value: TicketFormData): void;
  (e: 'submit'): void;
  (e: 'showResetDialog'): void;
}>();

const isValidMsisdn = computed(() => {
  if (!props.formData.MSISDN) return true;

  const lines = props.formData.MSISDN
    .split(/\n|\r\n?/)
    .map(line => line.trim())
    .filter(line => line.length > 0);

  return lines.every(line => /^\d+$/.test(line));
});

const isValidSubscriptionId = computed(() => {
  if (!props.formData.SubscriptionId) return true;

  const lines = props.formData.SubscriptionId
    .split(/\n|\r\n?/)
    .map(line => line.trim())
    .filter(line => line.length > 0);

  return lines.every(line => /^\d+$/.test(line));
});

const msisdnCount = computed(() => {
  if (!props.formData.MSISDN) return 0;
  return props.formData.MSISDN
    .split(/\n|\r\n?/)
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .length;
});

const subscriptionIdCount = computed(() => {
  if (!props.formData.SubscriptionId) return 0;
  return props.formData.SubscriptionId
    .split(/\n|\r\n?/)
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .length;
});

const hasDifferentLineCounts = computed(() => {
  if (!props.formData.SubscriptionId || props.formData.SubscriptionId.trim() === '') {
    return false;
  }

  return msisdnCount.value !== subscriptionIdCount.value;
});

function clearMsisdnError() {
  msisdnError.value = '';
  if (subscriptionIdError.value === 'Кількість рядків в MSISDN і SubscriptionId повинна бути однаковою') {
    subscriptionIdError.value = '';
  }
}

function clearSubscriptionIdError() {
  subscriptionIdError.value = '';
  if (msisdnError.value === 'Кількість рядків в MSISDN і SubscriptionId повинна бути однаковою') {
    msisdnError.value = '';
  }
}

function submitForm() {
  msisdnError.value = '';
  subscriptionIdError.value = '';

  if (!isValidMsisdn.value) {
    msisdnError.value = 'MSISDN повинен містити тільки цифри, без пробілів, кожен номер з нового рядка';
    return;
  }

  if (props.formData.SubscriptionId && props.formData.SubscriptionId.trim() !== '' && !isValidSubscriptionId.value) {
    subscriptionIdError.value = 'SubscriptionId повинен містити тільки цифри, без пробілів, кожен ID з нового рядка';
    return;
  }

  if (hasDifferentLineCounts.value) {
    msisdnError.value = 'Кількість рядків в MSISDN і SubscriptionId повинна бути однаковою';
    subscriptionIdError.value = 'Кількість рядків в MSISDN і SubscriptionId повинна бути однаковою';
    return;
  }

  emit('submit');
}
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease, max-height 0.3s ease, margin-bottom 0.3s ease;
  max-height: 1000px;
  transform: translateY(0);
  margin-bottom: 1rem;
  overflow: hidden;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
  margin-bottom: 0;
}
</style>
