<template>
  <div>
    <label :for="id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
      {{ label }}
    </label>
    <div class="mt-1">
      <textarea :id="id" :value="modelValue" :name="id" :rows="rows" :required="required" :placeholder="placeholder"
        class="input w-full auto-resize" :class="{ 'border-red-500 dark:border-red-500': error }" @input="handleInput"
        :style="textareaStyle" ref="textarea"></textarea>
      <slot name="counter"></slot>
      <div v-if="error" class="mt-1 text-xs text-red-500 dark:text-red-400">
        {{ error }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue';

defineProps<{
  id: string;
  label: string;
  modelValue: string;
  rows?: number;
  required?: boolean;
  placeholder?: string;
  error?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const textarea = ref<HTMLTextAreaElement | null>(null);
const height = ref<number | null>(null);

const textareaStyle = computed(() => {
  return {
    height: height.value ? `${height.value}px` : 'auto',
    maxHeight: '300px',
    overflowY: height.value && height.value >= 300 ? 'auto' as const : 'hidden' as const
  };
});

function adjustHeight() {
  if (textarea.value) {
    textarea.value.style.height = 'auto';
    const scrollHeight = textarea.value.scrollHeight;
    height.value = scrollHeight;
  }
}

function handleInput(event: Event) {
  const target = event.target as HTMLTextAreaElement;
  emit('update:modelValue', target.value);
  adjustHeight();
}

onMounted(() => {
  nextTick(() => {
    if (textarea.value && textarea.value.value) {
      adjustHeight();
    }
  });
});
</script>

<style scoped>
.auto-resize {
  resize: none;
  transition: height 0.1s ease;
  min-height: 2.5rem;
}
</style>
