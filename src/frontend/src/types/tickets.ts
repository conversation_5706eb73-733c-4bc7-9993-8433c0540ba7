export interface TicketFormData {
  MSISDN: string;
  SubscriptionId?: string;
  CategoryName: string;
  VirtualTeamName: string;
  ReactionName: string;
  ComplainDetails: string;
  Title: string;
  ResolutionSummary: string;
  TickerStatus: string;
  NotificationType: string;
  InformSubscriber: string;
  ResolutionReason: string;
}

export interface TicketEvent {
  status:
    | "created"
    | "started"
    | "resumed"
    | "processing"
    | "processed"
    | "waiting"
    | "completed"
    | "failed"
    | "error"
    | "cancelled"
    | "paused";
  total: number;
  processed: number;
  current?: string;
  message: string;
  timestamp?: string;
}

export interface TicketSession {
  sessionId: string;
  status:
    | "created"
    | "started"
    | "resumed"
    | "processing"
    | "processed"
    | "waiting"
    | "completed"
    | "failed"
    | "error"
    | "cancelled"
    | "paused";
  total: number;
  processed: number;
  events: TicketEvent[];
  totalEvents?: number;
  lastEvent?: TicketEvent;
  createdAt?: string;
  userId?: number;
  username?: string;
  isOwner?: boolean;
  paused?: boolean;
}
