import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import './assets/main.css';
import "vue-multiselect/dist/vue-multiselect.css";
import "./assets/styles/multiselect.css";
import "./assets/styles/vue-multiselect-dark.css";
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import "./assets/styles/vue-datepicker.css";
const app = createApp(App);
app.component("VueDatePicker", VueDatePicker);
app.use(createPinia());
app.use(router);
app.mount('#app');
