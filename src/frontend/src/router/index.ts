import { createRouter, createWebHistory } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import HomeView from '../views/HomeView.vue';
import LoginView from '../views/LoginView.vue';
import CreateTicketsView from '../views/CreateTicketsView.vue';
import SessionsView from '../views/SessionsView.vue';
import NotFoundView from '../views/NotFoundView.vue';
import ProfileView from "../views/ProfileView.vue";
import RequestPasswordResetView from "../views/RequestPasswordResetView.vue";
import ResetPasswordView from "../views/ResetPasswordView.vue";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "home",
      component: HomeView,
      meta: { requiresAuth: true },
    },
    {
      path: "/login",
      name: "login",
      component: LoginView,
      meta: { requiresAuth: false },
    },
    {
      path: "/create-tickets",
      name: "create-tickets",
      component: CreateTicketsView,
      meta: { requiresAuth: true, keepAlive: true },
    },
    {
      path: "/history",
      name: "sessions",
      component: SessionsView,
      meta: { requiresAuth: true, keepAlive: true },
    },
    {
      path: "/profile",
      name: "profile",
      component: ProfileView,
      meta: { requiresAuth: true },
    },
    {
      path: "/forgot-password",
      name: "forgot-password",
      component: RequestPasswordResetView,
      meta: { requiresAuth: false },
    },
    {
      path: "/reset-password",
      name: "reset-password",
      component: ResetPasswordView,
      meta: { requiresAuth: false },
    },
    {
      path: "/:pathMatch(.*)*",
      name: "not-found",
      component: NotFoundView,
    },
  ],
});

let authCheckedOnce = false;

export function resetAuthCheck() {
  authCheckedOnce = false;
}

router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore();

  if (!authCheckedOnce) {
    try {
      await authStore.checkAuth();
    } catch (error) {
      console.error("Failed to check authentication status in router guard:", error);
    } finally {
      authCheckedOnce = true;
    }
  }

  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      return next({ name: "login", query: { redirect: to.fullPath } });
    }
  } else if (to.name === "login" && authStore.isAuthenticated) {
    return next({ name: "home" });
  } else if (to.name === "reset-password" && authStore.isAuthenticated) {
    return next({ name: "home" });
  }

  next();
});

export default router;
