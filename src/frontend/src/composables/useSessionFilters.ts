import { ref } from 'vue';
import type { TicketSession } from '../types/tickets';

export function useSessionFilters() {
  const selectedUserId = ref<number>(-1);
  const selectedStatus = ref<string>('');
  const selectedDateRange = ref<[Date | null, Date | null]>([null, null]);

  const filterSessions = (allSessions: TicketSession[] | null): TicketSession[] => {
    if (!allSessions) return [];

    let filteredSessions = [...allSessions];

    if (selectedUserId.value !== -1) {
      filteredSessions = filteredSessions.filter(
        (session) => session.userId === selectedUserId.value
      );
    }

    if (selectedStatus.value !== "") {
      filteredSessions = filteredSessions.filter(
        (session) => session.status === selectedStatus.value
      );
    }

    if (selectedDateRange.value && selectedDateRange.value[0] && selectedDateRange.value[1]) {
      const startDate = new Date(selectedDateRange.value[0]);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(selectedDateRange.value[1]);
      endDate.setHours(23, 59, 59, 999);

      filteredSessions = filteredSessions.filter((session) => {
        if (!session.createdAt) return false;
        const sessionDate = new Date(session.createdAt);
        return sessionDate >= startDate && sessionDate <= endDate;
      });
    }

    return filteredSessions;
  };

  const setFilters = (userId: number, status: string, dateRange?: [Date | null, Date | null]) => {
    selectedUserId.value = userId;
    selectedStatus.value = status;
    selectedDateRange.value = dateRange || [null, null];
  };

  return {
    selectedUserId,
    selectedStatus,
    selectedDateRange,
    filterSessions,
    setFilters,
  };
}
