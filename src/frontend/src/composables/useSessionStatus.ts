import { ref, computed } from 'vue';
import type { TicketSession } from '../types/tickets';

export function useSessionStatus(session: TicketSession | null) {
  const isPauseLoading = ref(false);
  const isResumeLoading = ref(false);
  const isCancelLoading = ref(false);

  const isProcessCompleted = computed(() => {
    if (!session) return false;
    return ["completed", "cancelled", "failed"].includes(session.status);
  });

  const isProcessActive = computed(() => {
    if (!session) return false;
    return ["processing", "resumed"].includes(session.status);
  });

  const hasResumedAfterLastPause = computed(() => {
    if (!session?.events) return false;

    const events = session.events;
    const pauseEvents = events.filter((e) => e.status === "paused");
    const resumeEvents = events.filter((e) => e.status === "resumed");

    if (pauseEvents.length === 0) return false;
    if (resumeEvents.length === 0) return false;

    const lastPauseEvent = pauseEvents[pauseEvents.length - 1];
    const lastResumeEvent = resumeEvents[resumeEvents.length - 1];

    if (!lastPauseEvent.timestamp || !lastResumeEvent.timestamp) return false;

    return new Date(lastResumeEvent.timestamp) > new Date(lastPauseEvent.timestamp);
  });

  const canBeResumed = computed(() => {
    if (!session) return false;

    const isPaused =
      (session.status === "paused" && session.paused !== false) || session.paused === true;
    const isResumed =
      session.status === "resumed" || (session.status === "processing" && session.paused !== true);

    console.log(
      `canBeResumed check in useSessionStatus: status=${session.status}, paused=${session.paused}, isPaused=${isPaused}, isResumed=${isResumed}`
    );

    return isPaused && !isProcessCompleted.value && !isProcessActive.value && !isResumed;
  });

  function getStatusText(status: string | undefined): string {
    switch (status) {
      case "created":
        return "Створено";
      case "started":
        return "Розпочато";
      case "resumed":
        return "Відновлено";
      case "processing":
        return "В процесі";
      case "processed":
        return "Оброблено";
      case "waiting":
        return "Очікування";
      case "completed":
        return "Завершено";
      case "failed":
        return "Збій";
      case "error":
        return "Помилка";
      case "cancelled":
        return "Скасовано";
      case "paused":
        return "Призупинено";
      default:
        return "Невідомо";
    }
  }

  return {
    isPauseLoading,
    isResumeLoading,
    isCancelLoading,
    isProcessCompleted,
    isProcessActive,
    hasResumedAfterLastPause,
    canBeResumed,
    getStatusText,
  };
}
