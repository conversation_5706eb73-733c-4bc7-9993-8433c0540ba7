import { ref } from 'vue';

export function useConfirmationDialog() {
  const showCancelConfirmation = ref(false);
  const showResetConfirmation = ref(false);
  const showCreateConfirmation = ref(false);

  function toggleCancelDialog() {
    showCancelConfirmation.value = !showCancelConfirmation.value;
  }

  function toggleResetDialog() {
    showResetConfirmation.value = !showResetConfirmation.value;
  }

  function toggleCreateDialog() {
    showCreateConfirmation.value = !showCreateConfirmation.value;
  }

  return {
    showCancelConfirmation,
    showResetConfirmation,
    showCreateConfirmation,
    toggleCancelDialog,
    toggleResetDialog,
    toggleCreateDialog
  };
}
