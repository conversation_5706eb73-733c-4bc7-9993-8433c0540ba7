import { ref, Ref } from 'vue';
import { getCache, setCache } from '../services/cacheService';

interface UseCachedDataOptions<T> {
  cacheKey: string;
  fetchFunction: () => Promise<T>;
  compareFunction?: (cachedData: T, newData: T) => boolean;
  showUpdateNotification?: boolean;
  notificationMessage?: string;
}

export function useCachedData<T>(options: UseCachedDataOptions<T>) {
  const {
    cacheKey,
    fetchFunction,
    compareFunction = defaultCompare,
  } = options;

  const data = ref<T | null>(null) as Ref<T | null>;
  const isLoading = ref(true);
  const isBackgroundLoading = ref(false);
  const error = ref<string | null>(null);

  getCache<T>(cacheKey).then(cachedData => {
    if (cachedData) {
      data.value = cachedData;
      isLoading.value = false;
      fetchFreshData();
    }
  }).catch(err => {
    console.error(`Error loading initial cache for ${cacheKey}:`, err);
  });

  function defaultCompare(cachedData: T, newData: T): boolean {
    return JSON.stringify(cachedData) === JSON.stringify(newData);
  }

  async function loadData() {
    error.value = null;

    const cachedData = await getCache<T>(cacheKey);

    if (!cachedData && !data.value) {
      isLoading.value = true;
    }

    try {
      if (cachedData) {
        data.value = cachedData;
        isLoading.value = false;

        fetchFreshData();
      } else {
        await fetchFreshData();
        isLoading.value = false;
      }
    } catch (err) {
      console.error(`Error loading data for ${cacheKey}:`, err);
      error.value = err instanceof Error ? err.message : 'Помилка завантаження даних';
      isLoading.value = false;
    }
  }

  async function fetchFreshData() {
    isBackgroundLoading.value = true;

    try {
      const freshData = await fetchFunction();

      if (data.value) {
        const isDataChanged = !compareFunction(data.value, freshData);

        if (isDataChanged) {
          data.value = freshData;
          await setCache(cacheKey, freshData);
        }
      } else {
        data.value = freshData;
        await setCache(cacheKey, freshData);
      }
    } catch (err) {
      console.error(`Error fetching fresh data for ${cacheKey}:`, err);
      if (!data.value) {
        error.value = err instanceof Error ? err.message : 'Помилка завантаження даних';
      }
    } finally {
      isBackgroundLoading.value = false;
    }
  }

  async function refreshData() {
    isLoading.value = true;
    error.value = null;

    try {
      const freshData = await fetchFunction();

      data.value = freshData;
      await setCache(cacheKey, freshData);
    } catch (err) {
      console.error(`Error refreshing data for ${cacheKey}:`, err);
      error.value = err instanceof Error ? err.message : 'Помилка оновлення даних';
    } finally {
      isLoading.value = false;
    }
  }

  return {
    data,
    isLoading,
    isBackgroundLoading,
    error,
    loadData,
    refreshData,
    fetchFreshData
  };
}
