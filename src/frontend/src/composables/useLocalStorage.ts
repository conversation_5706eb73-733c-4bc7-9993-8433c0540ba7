import { watch } from 'vue';
import type { TicketFormData } from '../types/tickets';

export function useLocalStorage(formData: TicketFormData, defaultFormValues: TicketFormData) {
  function loadFormDataFromLocalStorage() {
    try {
      const savedData = localStorage.getItem('ticketFormData');
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        formData.CategoryName = parsedData.CategoryName || defaultFormValues.CategoryName;
        formData.VirtualTeamName = parsedData.VirtualTeamName || defaultFormValues.VirtualTeamName;
        formData.ReactionName = parsedData.ReactionName || defaultFormValues.ReactionName;
        formData.ComplainDetails = parsedData.ComplainDetails || defaultFormValues.ComplainDetails;
        formData.Title = parsedData.Title || defaultFormValues.Title;
        formData.ResolutionSummary = parsedData.ResolutionSummary || defaultFormValues.ResolutionSummary;
        formData.TickerStatus = parsedData.TickerStatus || defaultFormValues.TickerStatus;
        formData.NotificationType =
          parsedData.NotificationType || defaultFormValues.NotificationType;
        formData.InformSubscriber =
          parsedData.InformSubscriber || defaultFormValues.InformSubscriber;
        formData.ResolutionReason = parsedData.hasOwnProperty("ResolutionReason")
          ? parsedData.ResolutionReason
          : defaultFormValues.ResolutionReason;
      }
    } catch (error) {
      console.error('Error loading form data from localStorage:', error);
    }
  }

  function saveFormDataToLocalStorage() {
    try {
      const dataToSave = {
        CategoryName: formData.CategoryName,
        VirtualTeamName: formData.VirtualTeamName,
        ReactionName: formData.ReactionName,
        ComplainDetails: formData.ComplainDetails,
        Title: formData.Title,
        ResolutionSummary: formData.ResolutionSummary,
        TickerStatus: formData.TickerStatus,
        NotificationType: formData.NotificationType,
        InformSubscriber: formData.InformSubscriber,
        ResolutionReason: formData.ResolutionReason,
      };
      localStorage.setItem('ticketFormData', JSON.stringify(dataToSave));
    } catch (error) {
      console.error('Error saving form data to localStorage:', error);
    }
  }

  function resetFormToDefaults() {
    formData.CategoryName = defaultFormValues.CategoryName;
    formData.VirtualTeamName = defaultFormValues.VirtualTeamName;
    formData.ReactionName = defaultFormValues.ReactionName;
    formData.ComplainDetails = defaultFormValues.ComplainDetails;
    formData.Title = defaultFormValues.Title;
    formData.ResolutionSummary = defaultFormValues.ResolutionSummary;
    formData.TickerStatus = defaultFormValues.TickerStatus;
    formData.NotificationType = defaultFormValues.NotificationType;
    formData.InformSubscriber = defaultFormValues.InformSubscriber;
    formData.ResolutionReason = defaultFormValues.ResolutionReason;
    
    saveFormDataToLocalStorage();
  }

  watch(
    () => ({
      CategoryName: formData.CategoryName,
      VirtualTeamName: formData.VirtualTeamName,
      ReactionName: formData.ReactionName,
      ComplainDetails: formData.ComplainDetails,
      Title: formData.Title,
      ResolutionSummary: formData.ResolutionSummary,
      TickerStatus: formData.TickerStatus,
      NotificationType: formData.NotificationType,
      InformSubscriber: formData.InformSubscriber,
      ResolutionReason: formData.ResolutionReason,
    }),
    () => saveFormDataToLocalStorage(),
    { deep: true }
  );

  return {
    loadFormDataFromLocalStorage,
    saveFormDataToLocalStorage,
    resetFormToDefaults
  };
}
