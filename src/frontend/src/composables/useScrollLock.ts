import { ref, onMounted, onUnmounted } from "vue";

export function useScrollLock() {
  const isLocked = ref(false);
  let scrollPosition = 0;
  let scrollbarWidth = 0;

  onMounted(() => {
    scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
    document.documentElement.style.setProperty("--scrollbar-width", `${scrollbarWidth}px`);
  });

  const preventWheel = (e: WheelEvent) => {
    if (isLocked.value) {
      const target = e.target as HTMLElement;
      const eventsContainer = target.closest(".events-container");

      if (eventsContainer) {
        return;
      }

      e.preventDefault();
    }
  };

  const preventKeydown = (e: KeyboardEvent) => {
    if (
      isLocked.value &&
      (e.key === "ArrowDown" ||
        e.key === "ArrowUp" ||
        e.key === "Space" ||
        e.key === "PageDown" ||
        e.key === "PageUp" ||
        e.key === "Home" ||
        e.key === "End")
    ) {
      const target = e.target as HTMLElement;
      const eventsContainer = target.closest(".events-container");

      if (eventsContainer || document.activeElement?.closest(".events-container")) {
        return;
      }

      e.preventDefault();
    }
  };

  onUnmounted(() => {
    if (isLocked.value) {
      window.removeEventListener("wheel", preventWheel, { passive: false } as EventListenerOptions);
      window.removeEventListener("keydown", preventKeydown);
    }

    if (isLocked.value) {
      unlockScroll();
    }
  });

  function lockScroll() {
    if (isLocked.value) return;

    scrollPosition = window.scrollY;

    scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

    window.addEventListener("wheel", preventWheel, { passive: false });
    window.addEventListener("keydown", preventKeydown);

    document.documentElement.style.setProperty("--scrollbar-width", `${scrollbarWidth}px`);

    document.body.style.position = "fixed";
    document.body.style.top = `-${scrollPosition}px`;
    document.body.style.left = "0";
    document.body.style.right = "0";
    document.body.style.bottom = "0";
    document.body.style.width = "100%";
    document.body.style.overflowY = "scroll";

    isLocked.value = true;
  }

  function unlockScroll() {
    if (!isLocked.value) return;

    document.body.style.removeProperty("position");
    document.body.style.removeProperty("top");
    document.body.style.removeProperty("left");
    document.body.style.removeProperty("right");
    document.body.style.removeProperty("bottom");
    document.body.style.removeProperty("width");
    document.body.style.removeProperty("overflowY");

    window.removeEventListener("wheel", preventWheel, { passive: false } as EventListenerOptions);
    window.removeEventListener("keydown", preventKeydown);

    window.scrollTo(0, scrollPosition);

    isLocked.value = false;
  }

  return {
    isLocked,
    lockScroll,
    unlockScroll,
  };
}
