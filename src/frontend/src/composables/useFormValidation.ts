import { ref, computed } from 'vue';
import type { TicketFormData } from '../types/tickets';

export function useFormValidation(formData: TicketFormData) {
  const msisdnError = ref('');
  const subscriptionIdError = ref('');

  const msisdnCount = computed(() => {
    if (!formData.MSISDN) return 0;
    return formData.MSISDN
      .split(/\n|\r\n?/)
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .length;
  });

  const isValidMsisdn = computed(() => {
    if (!formData.MSISDN) return true;

    const lines = formData.MSISDN
      .split(/\n|\r\n?/)
      .map(line => line.trim())
      .filter(line => line.length > 0);

    return lines.every(line => /^\d+$/.test(line));
  });

  const subscriptionIdCount = computed(() => {
    if (!formData.SubscriptionId) return 0;
    return formData.SubscriptionId
      .split(/\n|\r\n?/)
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .length;
  });

  const isValidSubscriptionId = computed(() => {
    if (!formData.SubscriptionId) return true;

    const lines = formData.SubscriptionId
      .split(/\n|\r\n?/)
      .map(line => line.trim())
      .filter(line => line.length > 0);

    return lines.every(line => /^\d+$/.test(line));
  });

  const hasDifferentLineCounts = computed(() => {
    if (!formData.SubscriptionId || formData.SubscriptionId.trim() === '') {
      return false;
    }

    return msisdnCount.value !== subscriptionIdCount.value;
  });

  function clearMsisdnError() {
    msisdnError.value = '';
    if (subscriptionIdError.value === 'Кількість рядків в MSISDN і SubscriptionId повинна бути однаковою') {
      subscriptionIdError.value = '';
    }
  }

  function clearSubscriptionIdError() {
    subscriptionIdError.value = '';
    if (msisdnError.value === 'Кількість рядків в MSISDN і SubscriptionId повинна бути однаковою') {
      msisdnError.value = '';
    }
  }

  function validateForm(): boolean {
    msisdnError.value = '';
    subscriptionIdError.value = '';

    if (!isValidMsisdn.value) {
      msisdnError.value = 'MSISDN повинен містити тільки цифри, без пробілів, кожен номер з нового рядка';
      return false;
    }

    if (formData.SubscriptionId && formData.SubscriptionId.trim() !== '' && !isValidSubscriptionId.value) {
      subscriptionIdError.value = 'SubscriptionId повинен містити тільки цифри, без пробілів, кожен ID з нового рядка';
      return false;
    }

    if (hasDifferentLineCounts.value) {
      msisdnError.value = 'Кількість рядків в MSISDN і SubscriptionId повинна бути однаковою';
      subscriptionIdError.value = 'Кількість рядків в MSISDN і SubscriptionId повинна бути однаковою';
      return false;
    }

    if (!formData.InformSubscriber) {
      return false;
    }

    if (formData.ResolutionReason !== "" && !formData.ResolutionReason) {
      return false;
    }

    return true;
  }

  function processFormData(): TicketFormData {
    const processedFormData = { ...formData };

    if (processedFormData.MSISDN) {
      processedFormData.MSISDN = processedFormData.MSISDN
        .split(/\n|\r\n?/)
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .join(',');
    }

    if (processedFormData.SubscriptionId && processedFormData.SubscriptionId.trim() !== '') {
      processedFormData.SubscriptionId = processedFormData.SubscriptionId
        .split(/\n|\r\n?/)
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .join(',');
    } else {
      processedFormData.SubscriptionId = '';
    }

    return processedFormData;
  }

  return {
    msisdnError,
    subscriptionIdError,
    msisdnCount,
    subscriptionIdCount,
    isValidMsisdn,
    isValidSubscriptionId,
    hasDifferentLineCounts,
    clearMsisdnError,
    clearSubscriptionIdError,
    validateForm,
    processFormData
  };
}
