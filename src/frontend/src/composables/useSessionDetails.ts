import { ref, Ref } from 'vue';
import { getCache, setCache } from '../services/cacheService';
import { getSessionDetailsKey, getSessionEventsKey } from '../services/cacheService';
import { ticketsApi } from '../services/api';
import type { TicketSession, TicketEvent } from '../types/tickets';

export function useSessionDetails(sessionId: Ref<string | null>) {
  const session = ref<TicketSession | null>(null);
  const isLoadingSession = ref(false);
  const sessionError = ref<string | null>(null);

  const events = ref<TicketEvent[]>([]);
  const isLoadingEvents = ref(false);
  const isLoadingMoreEvents = ref(false);
  const eventsError = ref<string | null>(null);
  const currentOffset = ref(0);
  const hasMoreEvents = ref(true);
  const totalEvents = ref(0);

  async function loadSessionDetails() {
    if (!sessionId.value) return;

    sessionError.value = null;

    const cacheKey = getSessionDetailsKey(sessionId.value);
    const cachedData = await getCache<TicketSession>(cacheKey);

    if (!cachedData && !session.value) {
      isLoadingSession.value = true;
    }

    try {
      if (cachedData) {
        session.value = cachedData;
        isLoadingSession.value = false;

        fetchFreshSessionDetails();
      } else {
        await fetchFreshSessionDetails();
        isLoadingSession.value = false;
      }
    } catch (err) {
      console.error(`Error loading session details for ${sessionId.value}:`, err);
      sessionError.value =
        err instanceof Error ? err.message : "Помилка завантаження деталей сесії";
      isLoadingSession.value = false;
    }
  }

  async function fetchFreshSessionDetails() {
    if (!sessionId.value) return;

    try {
      const freshData = await ticketsApi.getSessionById(sessionId.value);

      if (session.value) {
        const isDataChanged = JSON.stringify(session.value) !== JSON.stringify(freshData);

        if (isDataChanged) {
          session.value = freshData;
          await setCache(getSessionDetailsKey(sessionId.value), freshData);
        }
      } else {
        session.value = freshData;
        await setCache(getSessionDetailsKey(sessionId.value), freshData);
      }
    } catch (err) {
      console.error(`Error fetching fresh session details for ${sessionId.value}:`, err);
      if (!session.value) {
        sessionError.value =
          err instanceof Error ? err.message : "Помилка завантаження деталей сесії";
      }
    }
  }

  async function loadEvents(offset = 0, append = false, batchSize = 50) {
    if (!sessionId.value) return;

    eventsError.value = null;

    const cacheKey = getSessionEventsKey(sessionId.value, batchSize, offset);
    const cachedData = await getCache<{
      events: TicketEvent[];
      totalEvents: number;
      offset: number;
      limit: number;
    }>(cacheKey);

    try {
      if (cachedData) {
        if (offset === 0 || !append) {
          events.value = cachedData.events;
        } else {
          const newEvents = cachedData.events.filter((newEvent) => {
            return !events.value.some((existingEvent) => {
              return (
                existingEvent.timestamp === newEvent.timestamp &&
                existingEvent.message === newEvent.message
              );
            });
          });

          events.value = [...events.value, ...newEvents];
        }

        if (
          session.value &&
          ["completed", "cancelled", "failed", "error"].includes(session.value.status)
        ) {
          totalEvents.value = cachedData.totalEvents;
        } else {
          totalEvents.value = Math.max(cachedData.totalEvents, events.value.length);
        }

        currentOffset.value = events.value.length;

        hasMoreEvents.value = currentOffset.value < totalEvents.value;

        fetchFreshEvents(offset, append, batchSize);
      } else {
        if (offset === 0 && !isLoadingEvents.value) {
          isLoadingEvents.value = true;
        } else if (offset > 0) {
          isLoadingMoreEvents.value = true;
        }

        await fetchFreshEvents(offset, append, batchSize);
        isLoadingEvents.value = false;
        isLoadingMoreEvents.value = false;
      }
    } catch (err) {
      console.error(`Error loading events for ${sessionId.value}:`, err);
      eventsError.value = err instanceof Error ? err.message : "Помилка завантаження подій сесії";
      isLoadingEvents.value = false;
      isLoadingMoreEvents.value = false;
    }
  }

  async function fetchFreshEvents(offset = 0, append = false, batchSize = 50) {
    if (!sessionId.value) return;

    try {
      const result = await ticketsApi.getSessionEvents(sessionId.value, batchSize, offset);

      await setCache(getSessionEventsKey(sessionId.value, batchSize, offset), result);

      if (offset === 0 || !append) {
        events.value = result.events;
      } else {
        const newEvents = result.events.filter((newEvent) => {
          return !events.value.some((existingEvent) => {
            return (
              existingEvent.timestamp === newEvent.timestamp &&
              existingEvent.message === newEvent.message
            );
          });
        });

        events.value = [...events.value, ...newEvents];
      }

      if (
        session.value &&
        ["completed", "cancelled", "failed", "error"].includes(session.value.status)
      ) {
        totalEvents.value = result.totalEvents;
      } else {
        totalEvents.value = Math.max(result.totalEvents, events.value.length);
      }

      currentOffset.value = events.value.length;

      hasMoreEvents.value = currentOffset.value < totalEvents.value;

      isLoadingEvents.value = false;
      isLoadingMoreEvents.value = false;
    } catch (err) {
      console.error(`Error fetching fresh events for ${sessionId.value}:`, err);
      if (events.value.length === 0) {
        eventsError.value = err instanceof Error ? err.message : "Помилка завантаження подій сесії";
      }
      isLoadingEvents.value = false;
      isLoadingMoreEvents.value = false;
    }
  }

  function reset() {
    session.value = null;
    events.value = [];
    currentOffset.value = 0;
    hasMoreEvents.value = true;
    totalEvents.value = 0;
  }

  return {
    session,
    events,
    totalEvents,
    currentOffset,
    hasMoreEvents,

    isLoadingSession,
    isLoadingEvents,
    isLoadingMoreEvents,
    sessionError,
    eventsError,

    loadSessionDetails,
    loadEvents,
    reset,
  };
}
