<template>
  <div class="min-h-screen flex flex-col">
    <Navbar v-if="isAuthenticated || $route.name === 'not-found'" />

    <main class="flex-grow">
      <router-view v-slot="{ Component, route }">
        <keep-alive v-if="route.meta.keepAlive">
          <component :is="Component" />
        </keep-alive>
        <component v-else :is="Component" />
      </router-view>
    </main>

    <footer v-if="$route.name !== 'not-found'" class="py-4 text-center text-sm text-gray-500 dark:text-gray-400">
      <p>© {{ currentYear }} <a href="https://cca.kyivstar.ua/" target="_blank" rel="noopener noreferrer">CCA</a></p>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from 'vue';
import { useAuthStore } from './stores/auth';
import { useThemeStore } from './stores/theme';
import { useTicketsStore } from './stores/tickets';
import { ticketsApi } from './services/api';
import Navbar from './components/layout/Navbar.vue';

const authStore = useAuthStore();
const themeStore = useThemeStore();
const ticketsStore = useTicketsStore();

const currentYear = new Date().getFullYear();
const isAuthenticated = computed(() => authStore.isAuthenticated);

watch(() => authStore.isAuthenticated, (isAuthenticated) => {
  if (!isAuthenticated) {
    localStorage.removeItem("currentSessionId");
    ticketsStore.resetSession();
  } else {
    verifyCurrentSession();
  }
});

async function verifyCurrentSession() {
  if (!authStore.isAuthenticated) {
    return;
  }

  const savedSessionId = localStorage.getItem("currentSessionId");
  if (savedSessionId) {
    try {
      const session = await ticketsStore.getSessionStatus(savedSessionId);

      if (!session) {
        localStorage.removeItem("currentSessionId");
        ticketsStore.resetSession();
      } else {
        const isFinalState = ["completed", "failed", "error", "cancelled"].includes(session.status);
        if (isFinalState) {
          localStorage.removeItem("currentSessionId");
          ticketsStore.resetSession();
        } else {
          const serverOwnership = session.isOwner === true;
          const localOwnership = ticketsStore.isSessionOwner(session);
          const isOwned = serverOwnership || localOwnership;

          if (!isOwned) {
            localStorage.removeItem("currentSessionId");
            ticketsStore.resetSession();
          }
        }
      }
    } catch (err) {
      console.error('Error verifying current session:', err);
    }
  } else {
    try {
      const activeSessions = await ticketsApi.getActiveSessions();

      if (activeSessions && activeSessions.length > 0) {
        const sortedSessions = [...activeSessions].sort((a, b) => {
          const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
          const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
          return dateB.getTime() - dateA.getTime();
        });

        const mostRecentSession = sortedSessions[0];

        localStorage.setItem("currentSessionId", mostRecentSession.sessionId);
      }
    } catch (err) {
      console.error('Error getting active sessions from server:', err);
    }
  }
}

onMounted(async () => {
  themeStore.initTheme();

  if (authStore.isAuthenticated) {
    await verifyCurrentSession();
  }
});
</script>
