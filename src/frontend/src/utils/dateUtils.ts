export function formatToKyivTime(dateString?: string): Date {
  if (!dateString) return new Date();
  const date = new Date(dateString);
  const utcTimestamp = date.getTime();
  const kyivTimestamp = utcTimestamp + 10800000;

  return new Date(kyivTimestamp);
}

export function formatDateToLocaleString(dateString?: string): string {
  if (!dateString) return "Невідомо";
  const kyivDate = formatToKyivTime(dateString);

  return new Intl.DateTimeFormat("uk-UA", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  }).format(kyivDate);
}

export function formatDateLong(dateString?: string): string {
  if (!dateString) return "Невідомо";
  const kyivDate = formatToKyivTime(dateString);

  return new Intl.DateTimeFormat("uk-UA", {
    day: "numeric",
    month: "long",
    year: "numeric",
  }).format(kyivDate);
}

export function formatLogsDate(dateString?: string): string {
  if (!dateString) return "Невідомо";
  const date = new Date(dateString);
  const utcTimestamp = date.getTime();
  const kyivTimestamp = utcTimestamp;
  const kyivDate = new Date(kyivTimestamp);

  return new Intl.DateTimeFormat("uk-UA", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  }).format(kyivDate);
}
