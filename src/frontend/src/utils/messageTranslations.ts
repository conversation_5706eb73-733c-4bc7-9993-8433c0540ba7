export function translateEventMessage(message: string): string {
  if (message === 'Processing started') {
    return 'Обробку розпочато';
  }
  
  if (message === 'Process completed successfully') {
    return 'Процес успішно завершено';
  }
  
  if (message === 'All MSISDN processed successfully') {
    return 'Всі MSISDN успішно оброблено';
  }
  
  if (message === 'Processing paused by user request') {
    return 'Обробку призупинено за запитом користувача';
  }
  
  if (message === 'Processing resumed by user request') {
    return 'Обробку відновлено за запитом користувача';
  }
  
  if (message === 'Processing cancelled by user request') {
    return 'Обробку скасовано за запитом користувача';
  }
  
  if (message === 'Waiting 5 seconds before processing next MSISDN') {
    return 'Очікування 5 секунд перед обробкою наступного MSISDN';
  }
  
  const processingPattern = /^Processing MSISDN (\d+) \((\d+)\/(\d+)\)$/;
  if (processingPattern.test(message)) {
    const matches = message.match(processingPattern);
    if (matches && matches.length === 4) {
      const [_, msisdn, current, total] = matches;
      return `Обробка MSISDN ${msisdn} (${current}/${total})`;
    }
  }
  
  const processedPattern = /^MSISDN (\d+) processed \((\d+)\/(\d+)\)$/;
  if (processedPattern.test(message)) {
    const matches = message.match(processedPattern);
    if (matches && matches.length === 4) {
      const [_, msisdn, current, total] = matches;
      return `MSISDN ${msisdn} оброблено (${current}/${total})`;
    }
  }
  
  const resumedFromPattern = /^Processing resumed from (\d+)\/(\d+)$/;
  if (resumedFromPattern.test(message)) {
    const matches = message.match(resumedFromPattern);
    if (matches && matches.length === 3) {
      const [_, processed, total] = matches;
      return `Обробку відновлено з ${processed}/${total}`;
    }
  }
  
  return message;
}
