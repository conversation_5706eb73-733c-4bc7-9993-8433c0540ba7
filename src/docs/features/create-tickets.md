# Створення тікетів

Ця сторінка описує процес масового створення тікетів у системі CM Echo.

## Доступ до форми створення тікетів

Для доступу до форми створення тікетів:

1. Увійдіть у систему.
2. Натисніть на пункт меню "Тікети" у верхньому меню.
3. Або натисніть кнопку "Створити тікети" на головній сторінці.

## Заповнення форми

Форма створення тікетів містить наступні поля:

### MSISDN

- Список особових рахунків абонентів (по одному на рядок) - обов'язкове поле.

### SubscriptionId

- Список Subscription-ID абонентів (по одному на рядок) є обов'язковим тільки у тому випадку, якщо тікети створюються по абонентам FMC або по анульованим особовим рахункам. В СМ міститься у viewSubscription, наприклад [посилання](https://cm.kyivstar.ua/scripts/ticket.fcgi?_sf=0&action=doScreenDefinition&idString=viewSubscription&entryId=75222).

![Subscription-ID](../screenshots/1.png)

::: warning Важливо
Якщо ви вже вказуєте SubscriptionId, то кількість MSISDN повинна дорівнювати кількості SubscriptionId. Система не дозволить створити тікети, якщо кількість не співпадає.
:::

### Додаткові поля

| Поле                  | Опис                                                                                                                                                                                                                                                                                                                                                                |
| --------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **CategoryName**      | Назва категорії **Case category**, до якої буде віднесено тікет, в СМ позначається як _ticket.x_case_category.x_fullvalue_. Наприклад **"Домашний Интернет/Segment FTTB/Услуги ФТТБ/Домашний Интернет/Жалоба Домашний Интернет: Технические жалобы/Сетевой кабель не подключен/Доступність комутатора: Немає даних з порту / Порожньо / Абонент відсутній у базі"** |
| **VirtualTeamName**   | Тут треба вказати VirtualTeam для тікета. Ім'я має бути повним, наприклад: **"VLP_FTTB/Технический специалист FTTB"**.                                                                                                                                                                                                                                              |
| **ReactionName**      | Реакція компанії, в СМ позначається як Reaction of company або _ticket.x_action_to_resolve_.x_reaction. наприклад **"Звернення збережено з реакцією на технічного спеціаліста"**                                                                                                                                                                                    |
| **Title**             | Заголовок тікета, приймає будь-яке текстове значення. Наприклад: **"Жалоба Домашний Интернет: Технические жалобы/Роутер НЕ Київстар"**                                                                                                                                                                                                                              |
| **ComplainDetails**   | Текстовий вміст внутрішнього повідомлення, доданого до тікета. Може містити довільний текст, наприклад: **"У клієнта виникла проблема з роутером"**.                                                                                                                                                                                                                |
| **ResolutionSummary** | Resolution Summary тікета, в СМ так і позначається, або _ticket.x_resolution_summary_. Може містити довільний текст.                                                                                                                                                                                                                                                |
| **TickerStatus**      | Внутрішній статус тікета (_ticket.status_): Активний, Закритий, Відкладений, Видалений, Складений                                                                                                                                                                                                                                                                   |
| **NotificationType**  | Тип сповіщення абонента, в СМ позначається як _ticket.x_notification_type_. При створенні скарги виглядає отак: ![InformSubscriber](../screenshots/9.png)                                                                                                                                                                                                           |
| **InformSubscriber**  | Спосіб інформування абонента, в СМ позначається як _ticket.x_inform_subscriber_. При створенні скарги виглядає отак: ![InformSubscriber](../screenshots/7.png)                                                                                                                                                                                                      |
| **ResolutionReason**  | Спосіб інформування абонента який вказується при закритті скарги. Якщо, нічого не обирати, тобто вказати **"Оберіть значення"**, то при створенні тікетів це поле буде просто пустим. В СМ позначається як _ticket.x_resolution_reason_. В СМ знаходиться у **"Вирішити скаргу"** і має такий вигляд: ![ResolutionReason](../screenshots/8.png)                                                                                                                                          |

::: danger Важливо
Заповнюйте додакові поля уважно, звіряйтесь з актуальними на даний момент значеннями СМ, щоб уникнути невалідних тікетів або збоїв у процесі створення. Краще витратити час, і все перевірити, або перепитати в когось.
:::

## Лічильники рядків

Під полями MSISDN та SubscriptionId відображаються лічильники рядків, які показують кількість введених значень. Це допомагає переконатися, що кількість MSISDN та SubscriptionId співпадає.

![Лічильники рядків](../screenshots/0.png)

## Створення тікетів

Після заповнення всіх необхідних полів:

1. Натисніть кнопку **"Старт"**.
2. Підтвердіть створення у діалоговому вікні.
3. Система почне процес створення тікетів.

## Збереження налаштувань

Система автоматично зберігає ваші налаштування форми (крім MSISDN та SubscriptionId) у локальному сховищі браузера. При наступному відкритті форми ці налаштування будуть автоматично заповнені.

## Скидання значень полів

Поряд з кнопкою **Старт** є кнопка **Скинути** яка дозволяє відновити стандартні значення полів.

1. Натисніть кнопку **"Скинути"**.
2. Підтвердіть дію у діалоговому вікні.
3. Значення полів відновляться до типових значень.

## Моніторинг процесу

Після початку процесу створення тікетів ви можете:

- Спостерігати за прогресом у реальному часі.
- Переглядати логи подій.
- Керувати процесом (призупиняти, відновлювати, скасовувати).

![Моніторинг процесу](../screenshots/2.png)

## Детальна інформація про сесію

Під час створення тікетів ви можете натиснути на ідентифікатор сесії, щоб відкрити детальну інформацію про поточну сесію.

![Деталі сесії](../screenshots/3.png)
