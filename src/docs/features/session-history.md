# Історія сесій

Ця сторінка описує функціональність перегляду історії сесій створення тікетів у системі CM Echo.

## Доступ до історії сесій

Для доступу до історії сесій:

1. Увійдіть у систему
2. Натисніть на пункт меню "Історія" у верхньому меню

## Огляд сторінки історії

Сторінка історії сесій містить таблицю з усіма сесіями створення тікетів. Для кожної сесії відображається:

- **ID** - унікальний ідентифікатор сесії
- **Користувач** - ім'я користувача, який створив сесію
- **Дата створення** - дата та час створення сесії
- **Статус** - поточний статус сесії
- **Прогрес** - прогрес виконання сесії

![Історія](../screenshots/5.png)

## Фільтрація сесій

Ви можете фільтрувати сесії за:

- **Користувачем** - виберіть користувача зі списку
- **Статусом** - виберіть статус зі списку (В обробці, Призупинено, Завершено, Скасовано тощо)

Для застосування фільтрів виберіть потрібні значення у відповідних випадаючих списках.

## Детальна інформація про сесію

Для перегляду детальної інформації про сесію:

1. Натисніть на ідентифікатор сесії в таблиці
2. Відкриється діалогове вікно з детальною інформацією

### Вміст діалогового вікна

Діалогове вікно містить:

- **Загальну інформацію** - ID, дата створення, статус, прогрес
- **Логи подій** - хронологічний список усіх подій сесії

### Пошук у логах подій

Ви можете шукати конкретні події у логах:

1. Введіть пошуковий запит у поле пошуку
2. Результати будуть автоматично відфільтровані

Пошук працює по всьому тексту подій, включаючи дати та повідомлення.
