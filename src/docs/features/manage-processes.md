# Керування процесами

Ця сторінка описує можливості керування процесами створення тікетів у системі CM Echo.

![Керування процесами](../screenshots/4.png)

## Доступні дії

Під час активного процесу створення тікетів ви маєте доступ до наступних дій:

### Призупинення процесу

Ви можете тимчасово призупинити процес створення тікетів:

1. Натисніть кнопку "Призупинити" (фіолетова кнопка)
2. Процес буде призупинено після завершення поточної операції

::: tip Корисно знати
Призупинення процесу дозволяє вам тимчасово зупинити створення тікетів без втрати прогресу. Ви можете відновити процес пізніше.
:::

### Відновлення процесу

Якщо процес був призупинений, ви можете відновити його:

1. Натисніть кнопку "Відновити" (зелена кнопка)
2. Процес продовжиться з місця, де він був призупинений

### Скасування процесу

Ви можете повністю скасувати процес створення тікетів:

1. Натисніть кнопку "Скасувати" (червона кнопка)
2. Підтвердіть дію у діалоговому вікні
3. Процес буде скасовано, і подальші тікети не будуть створені

::: warning Увага
Скасування процесу є незворотною дією. Вже створені тікети залишаться в системі, але подальші тікети не будуть створені.
:::

## Статуси процесу

Процес створення тікетів може мати наступні статуси:

- **В процесі** - процес активно створює тікети
- **Призупинено** - процес тимчасово призупинено
- **Завершено** - процес успішно завершено
- **Скасовано** - процес був скасований користувачем
- **Збій** - процес завершився зі збоєм на стороні сервера
- **Помилка** - процес завершився з помилкою на стороні клієнта
- **Відновлено** - процес було відновлено після призупинення

## Прогрес процесу

Під час процесу створення тікетів ви можете спостерігати за прогресом:

- Прогрес-бар показує відсоток завершення
- Числовий індикатор показує кількість оброблених тікетів та загальну кількість
- Логи подій показують детальну інформацію про останні події

::: tip Корисно знати
Особливістю системи CM Echo є те, що процес створення тікетів продовжується навіть якщо ви закриєте браузер або вийдете з системи. Це дозволяє запустити довготривалий процес і повернутися до нього пізніше.
:::

