<template>
  <Layout>
    <template #layout-top>
    </template>
  </Layout>
</template>

<script setup>
import { onMounted } from 'vue'
import DefaultTheme from 'vitepress/theme'

const { Layout } = DefaultTheme

onMounted(() => {
  const themeScript = document.createElement('script')
  themeScript.src = './theme-handler.js'
  document.body.appendChild(themeScript)

  const linkScript = document.createElement('script')
  linkScript.src = './link-handler.js'
  document.body.appendChild(linkScript)
})
</script>
