window.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'theme-change') {
    const theme = event.data.theme;
    
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
      localStorage.setItem('vitepress-theme', 'dark');
    } else if (theme === 'light') {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('vitepress-theme', 'light');
    } else if (theme === 'auto') {
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
      localStorage.removeItem('vitepress-theme');
    }
  }
});
