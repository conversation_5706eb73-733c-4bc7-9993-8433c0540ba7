document.addEventListener('DOMContentLoaded', () => {
  function handleLinkClick(event) {
    const link = event.target.closest('a');
    if (!link) return;
    const href = link.getAttribute('href');
    if (href === '/cm-echo' || href.startsWith('/cm-echo/') && !href.startsWith('/cm-echo/docs')) {
      event.preventDefault();
      window.parent.postMessage({
        type: 'link-click',
        href: href
      }, '*');
    }
  }
  document.body.addEventListener('click', handleLinkClick);
});
