#!/usr/bin/env node

import { rm } from 'fs/promises';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const vitepressDir = join(__dirname, '.vitepress');
const cacheDir = join(vitepressDir, 'cache');
const distDir = join(vitepressDir, 'dist');

async function cleanDirectories() {
  console.log('🧹 Cleaning VitePress cache and dist directories...');
  
  try {
    await rm(cacheDir, { recursive: true, force: true });
    console.log('✅ Cache directory cleaned');
  } catch (error) {
    console.log('⚠️ No cache directory found or error cleaning it');
  }
  
  try {
    await rm(distDir, { recursive: true, force: true });
    console.log('✅ Dist directory cleaned');
  } catch (error) {
    console.log('⚠️ No dist directory found or error cleaning it');
  }
  
  console.log('🎉 Cleaning complete! You can now rebuild the documentation.');
}

cleanDirectories().catch(error => {
  console.error('❌ Error cleaning directories:', error);
  process.exit(1);
});
