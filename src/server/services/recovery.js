import { getIncompleteSessions, getAllSessions } from '../db/index.js';
import { processTicketsAsync } from './process-tickets.js';
import console from '../lib/logging.js';

/**
 * Map to store active client connections for each session.
 * Key: sessionId, Value: Set of client connections
 * @type {Map<string, Set<Object>>}
 */
const activeClients = new Map();

/**
 * Cleans up expired sessions from the activeClients map.
 * A session is considered expired if it's in a terminal state (completed, cancelled, error)
 * and has no active clients.
 * 
 * @returns {Promise<number>} The number of sessions cleaned up
 */
export async function cleanupExpiredSessions() {
  try {
    let cleanedCount = 0;

    // Check all sessions in the activeClients map
    for (const [sessionId, clients] of activeClients.entries()) {
      if (clients.size === 0) {
        // Get the session from the database to check its status
        const sessions = await getAllSessions();
        const session = sessions.find(s => s.id === sessionId);

        if (!session || ['completed', 'cancelled', 'error'].includes(session.status)) {
          activeClients.delete(sessionId);
          cleanedCount++;
          console.debug(`Cleaned up expired session ${sessionId} from activeClients map`);
        }
      }
    }

    if (cleanedCount > 0) {
      console.debug(`Cleaned up ${cleanedCount} expired sessions`);
    }

    return cleanedCount;
  } catch (error) {
    console.error('Error cleaning up expired sessions:', error);
    return 0;
  }
}



export { activeClients };

/**
 * Recovers incomplete sessions from the database after application restart.
 * Finds all sessions with 'processing' status that weren't cancelled,
 * adds a recovery event to each session, and restarts the ticket processing.
 *
 * @async
 * @returns {Promise<number>} The number of sessions recovered, or 0 if none or error
 */
async function recoverIncompleteSessions() {
  try {
    const incompleteSessions = await getIncompleteSessions();

    if (incompleteSessions.length === 0) {
      console.debug('No incomplete sessions to recover');
      return 0;
    }

    console.info(`Found ${incompleteSessions.length} incomplete sessions to recover`);

    for (const session of incompleteSessions) {
      console.info(`[${session.requestId}] Recovering session ${session.id} (${session.processed}/${session.total} processed)`);

      activeClients.set(session.id, session.clients);

      const recoveryEvent = {
        status: 'recovered',
        total: session.total,
        processed: session.processed,
        message: `Session recovered after application restart (${session.processed}/${session.total} processed)`
      };
      session.events.push(recoveryEvent);

      processTicketsAsync(session);
    }

    return incompleteSessions.length;
  } catch (error) {
    console.error('Error recovering incomplete sessions:', error);
    return 0;
  }
}

export { recoverIncompleteSessions };
