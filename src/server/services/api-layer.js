import fetch from 'node-fetch';
import console from '../lib/logging.js';
import { HttpsProxyAgent } from 'https-proxy-agent';
import https from 'https';

/**
 * Creates a ticket in the external ticketing system.
 *
 * @param {string} requestId - Unique identifier for the logs
 * @param {string} MSISDN - Personal account of the subscriber
 * @param {string} SubscriptionId - Identifier for the account
 * @param {string} CategoryName - Category of the ticket
 * @param {string} VirtualTeamName - Name of the virtual team to assign the ticket to
 * @param {string} ReactionName - Type of reaction required
 * @param {string} ComplainDetails - Details of the complaint
 * @param {string} Title - Title of the ticket
 * @param {string} ResolutionSummary - Summary of the resolution
 * @param {string} TickerStatus - Internal status ID (1 - active, 2 - closed, 3 - postponed, 4 - deleted, 5 - composed)
 * @param {string} NotificationType - Notification type (1 - Email, 2 - SMS, 3 - None, 4 - Callback)
 * @param {string} InformSubscriber - Flag indicating whether to inform the subscriber about the ticket. Available values: 1 - (By phone call), 2 - (By decision - SMS, no further updates), 3 - (By SMS), 4 - (Not needed), 5 - (By email)
 * @param {string} ResolutionReason - The reason for the resolution. Available values: 4 - (Informed by call), 6 - (Informed by SMS), 19 - (Information was not needed)
 * @returns {Promise<void>} - Promise that resolves when the ticket creation process completes
 */
export async function createTicket(MSISDN, SubscriptionId, CategoryName, VirtualTeamName, ReactionName, ComplainDetails, Title, ResolutionSummary, TickerStatus, NotificationType, InformSubscriber, ResolutionReason) {
  const headersList = {
    'Accept': '*/*',
    'Authorization': process.env.API_LAYER_AUTH,
    'Content-Type': 'application/json'
  };

  const bodyContent = JSON.stringify({
    'scriptName': 'createTicket',
    'items': [
      {
        'name': 'MSISDN',
        'value': MSISDN
      },
      {
        'name': 'SubscriptionId',
        'value': SubscriptionId
      },
      {
        'name': 'CategoryName',
        'value': CategoryName
      },
      {
        'name': 'VirtualTeamName',
        'value': VirtualTeamName
      },
      {
        'name': 'ReactionName',
        'value': ReactionName
      },
      {
        'name': 'ComplainDetails',
        'value': ComplainDetails
      },
      {
        'name': 'priority',
        'value': '2'
      },
      {
        'name': 'Title',
        'value': Title
      },
      {
        'name': 'Author',
        'value': 'CCA'
      },
      {
        'name': 'TicketType',
        'value': '1'
      },
      {
        'name': 'sLevel',
        'value': '2'
      },
      {
        'name': 'parameter.0.field',
        'value': 'ticket.x_notification_type'
      },
      {
        'name': 'parameter.0.value',
        'value': NotificationType
      },
      {
        'name': 'parameter.1.field',
        'value': 'ticket.status'
      },
      {
        'name': 'parameter.1.value',
        'value': TickerStatus
      },
      {
        'name': 'parameter.2.field',
        'value': 'ticket.x_inform_subscriber'
      },
      {
        'name': 'parameter.2.value',
        'value': InformSubscriber
      },
      {
        'name': 'parameter.3.field',
        'value': 'ticket.x_resolution_summary'
      },
      {
        'name': 'parameter.3.value',
        'value': ResolutionSummary
      },
      {
        'name': 'parameter.4.field',
        'value': 'ticket.ticket_status'
      },
      {
        'name': 'parameter.4.value',
        'value': TickerStatus
      },
      {
        'name': 'parameter.5.field',
        'value': 'ticket.x_resolution_reason'
      },
      {
        'name': 'parameter.5.value',
        'value': ResolutionReason
      }
    ]
  });

  try {
    const timeout = 30 * 1000;
    const response = await fetch('https://api.kyivstar.ua/v1.0/case-management/tickets', {
      method: 'POST',
      headers: headersList,
      body: bodyContent,
      agent: process.env.MODE === 'DEV' ?
        new HttpsProxyAgent(process.env.PROXY_URL) :
        new https.Agent({ rejectUnauthorized: false }),
      timeout: timeout
    });

    const json = await response.json();
    console.debug(json);

    if (response.ok) {
      console.debug(`Ticket created successfully, ticketId - ${json.resource.ticketId}`);
      return `Ticket created, ticketId - ${json.resource.ticketId}`;
    } else {
      if (json.errorCode === 161) {
        console.warn(`Ticket not created, errorCode: ${json.errorCode}, reason: ${json.errorMsg}`);
        return `Ticket not created, reason: ${json.errorMsg}`;
      } else {
        console.error(`API error occurred, errorCode: ${json.errorCode}, errorMsg: ${json.errorMsg}`);
        return `Error: ${json.errorMsg || error.message}`;
      }
    }
  } catch (error) {
    if (error.type === 'request-timeout') {
      console.error(`Request timed out after ${timeout} seconds`);
      return `Error: Request timed out after ${timeout} seconds`;
    } else {
      console.error('Unexpected error occurred:', error);
      return `Error: ${error.message}`;
    }
  }
}
