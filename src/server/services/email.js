import nodemailer from 'nodemailer';
import console from '../lib/logging.js';

/**
 * Sends an email using the configured SMTP server.
 *
 * @param {string} email - Recipient's email address
 * @param {string} subject - Email subject line
 * @param {string} text - HTML content of the email
 * @returns {Promise<boolean>} - True if email was sent successfully, false otherwise
 */
export default async function sendEmail(email, subject, text) {
  const transporter = nodemailer.createTransport({
    host: 'email.kyivstar.net',
    port: 25,
    secure: false,
    tls: {
      rejectUnauthorized: false,
      minVersion: 'TLSv1.2'
    }
  });

  const mailOptions = {
    from: 'CCA <<EMAIL>>',
    to: email,
    subject: subject,
    html: text
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.debug('Email sent: ' + info.response);
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
}

