import console from '../lib/logging.js';
import { createTicket } from './api-layer.js';
import { updateSession, addSessionEvent, getSession } from '../db/index.js';
import { activeClients as recoveryClients } from './recovery.js';

/**
 * Creates a promise that resolves after the specified time.
 *
 * @param {number} ms - Time to sleep in milliseconds
 * @returns {Promise<void>} - Promise that resolves after the specified time
 */
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Processes tickets asynchronously based on the provided session data.
 * Handles the entire lifecycle of ticket processing including starting, pausing,
 * resuming, and completing the process. Sends real-time updates to connected clients.
 *
 * @param {Object} session - Session object containing ticket processing information
 * @param {string} session.id - Unique identifier for the session
 * @param {string} session.requestId - Request identifier for logging
 * @param {Object} session.data - Data needed for ticket creation
 * @param {string[]} session.data.MSISDNs - Array of mobile subscriber numbers to process
 * @param {string[]} session.data.subscriptionIds - Array of subscription IDs corresponding to MSISDNs
 * @param {string} session.data.CategoryName - Category name for tickets
 * @param {string} session.data.VirtualTeamName - Virtual team name for tickets
 * @param {string} session.data.ReactionName - Reaction name for tickets
 * @param {string} session.data.ComplainDetails - Complaint details for tickets
 * @param {string} session.data.Title - Title for tickets
 * @param {string} session.data.ResolutionSummary - Resolution summary for tickets
 * @param {string} session.data.TickerStatus - Ticker status for tickets
 * @param {string} session.data.NotificationType - Notification type
 * @param {string} session.data.InformSubscriber - Inform subscriber for tickets
 * @param {string} session.data.ResolutionReason - Resolution reason for tickets
 * @param {Array} session.events - Array of events that have occurred during processing
 * @param {number} session.processed - Number of tickets already processed
 * @param {boolean} session.paused - Whether the session is currently paused
 * @param {boolean} session.cancelled - Whether the session has been cancelled
 * @param {string} session.status - Current status of the session
 * @returns {Promise<void>} - Promise that resolves when processing is complete
 */
export async function processTicketsAsync(session) {
  const {
    id: sessionId,
    requestId,
    data: {
      MSISDNs,
      subscriptionIds,
      CategoryName,
      VirtualTeamName,
      ReactionName,
      ComplainDetails,
      Title,
      ResolutionSummary,
      TickerStatus,
      NotificationType,
      InformSubscriber,
      ResolutionReason
    }
  } = session;

  /**
   * Adds an event to the session and notifies all connected clients.
   *
   * @param {Object} event - Event object to add to the session
   * @param {string} [event.timestamp] - Timestamp of the event (added if not provided)
   * @param {number} [event.total] - Total number of items to process (added if not provided)
   * @param {number} [event.processed] - Number of items processed so far (added if not provided)
   * @param {string} event.status - Status of the event
   * @param {string} [event.message] - Message describing the event
   * @returns {Promise<void>} - Promise that resolves when the event has been added and clients notified
   */
  const addEvent = async (event) => {
    if (!event.timestamp) {
      event.timestamp = new Date().toISOString();
    }

    if (typeof event.total !== 'number') {
      event.total = MSISDNs.length;
    }

    if (typeof event.processed !== 'number' && event.status !== 'error') {
      event.processed = session.processed || 0;
    }

    console.debug(`[${requestId}]`, `Adding event to session ${sessionId}:`, JSON.stringify(event));

    session.events.push(event);

    const success = await addSessionEvent(sessionId, event);
    if (!success) {
      console.warn(`[${requestId}]`, `Failed to add event to database for session ${sessionId}`);
      return;
    }

    const clients = recoveryClients.get(sessionId) || new Set();

    console.debug(`[${requestId}]`, `Notifying ${clients.size} clients for session ${sessionId}`);

    const eventCopy = JSON.parse(JSON.stringify(event));

    console.debug(`[${requestId}]`, `Sending event to clients: ${JSON.stringify(eventCopy)}`);

    await Promise.all(
      Array.from(clients).map(async (client) => {
        if (!client.writableEnded) {
          try {
            const message = `data: ${JSON.stringify(eventCopy)}\n\n`;

            await new Promise((resolve, reject) => {
              setTimeout(() => {
                try {
                  client.write(message);
                  if (typeof client.flush === 'function') {
                    client.flush();
                  }
                  resolve();
                } catch (err) {
                  console.error(`[${requestId}]`, `Error writing to client for session ${sessionId}:`, err);
                  reject(err);
                }
              }, 50);
            });
          } catch (err) {
            console.error(`[${requestId}]`, `Error sending event to client for session ${sessionId}:`, err);
          }
        } else {
          console.debug(`[${requestId}]`, `Client for session ${sessionId} is no longer writable`);
        }
      })
    );

    await new Promise(resolve => setTimeout(resolve, 200));
  };

  const isRecovered = session.processed > 0;
  const isResumed = session.status === 'resumed';

  if (isResumed && session.cancelled) {
    session.cancelled = false;
    console.debug(`[${requestId}]`, `Resetting cancelled flag for resumed session ${sessionId}`);
  }

  console.debug(`[${requestId}]`, `Processing session ${sessionId}:`, {
    status: session.status,
    paused: session.paused,
    cancelled: session.cancelled,
    processed: session.processed,
    total: session.total,
    isRecovered,
    isResumed
  });

  if (!isResumed) {
    session.status = 'processing';
    await updateSession(sessionId, { status: 'processing' });
  }

  if (!isRecovered && !isResumed) {
    await addEvent({
      status: 'started',
      total: MSISDNs.length,
      processed: 0,
      message: 'Processing started',
      timestamp: new Date().toISOString()
    });
  } else if (isRecovered && !isResumed) {
    await addEvent({
      status: 'resumed',
      total: MSISDNs.length,
      processed: session.processed,
      message: `Processing resumed from ${session.processed}/${MSISDNs.length}`,
      timestamp: new Date().toISOString()
    });
  }
  try {
    const latestSession = await getSession(sessionId);
    if (latestSession) {
      session.paused = latestSession.paused;
      session.status = latestSession.status;
      session.cancelled = latestSession.cancelled;

      console.debug(`[${requestId}]`, `Updated session from database: status=${session.status}, paused=${session.paused}, cancelled=${session.cancelled}`);
    }

    if (session.paused) {
      console.debug(`[${requestId}]`, `Session ${sessionId} is paused, not starting processing`);
      return;
    }

    console.debug(`[${requestId}]`, `Starting/resuming processing for session ${sessionId} with status=${session.status}, paused=${session.paused}`);


    for (let i = session.processed; i < MSISDNs.length; i++) {
      const latestSession = await getSession(sessionId);
      if (latestSession) {
        session.paused = latestSession.paused;
        session.status = latestSession.status;
        session.cancelled = latestSession.cancelled;
      }

      if (session.cancelled) {
        console.debug(`[${requestId}]`, `Processing stopped after ${i} MSISDN due to cancellation`);
        break;
      }

      if (session.paused) {
        console.debug(`[${requestId}]`, `Processing paused after ${i} MSISDN`);
        break;
      }

      const MSISDN = MSISDNs[i];
      const SubscriptionId = subscriptionIds[i];

      await addEvent({
        status: 'processing',
        total: MSISDNs.length,
        processed: i,
        current: MSISDN,
        message: `Processing MSISDN ${MSISDN} (${i + 1}/${MSISDNs.length})`,
        timestamp: new Date().toISOString()
      });

      const ticketResult = await createTicket(
        requestId,
        MSISDN,
        SubscriptionId,
        CategoryName,
        VirtualTeamName,
        ReactionName,
        ComplainDetails,
        Title,
        ResolutionSummary,
        TickerStatus,
        NotificationType,
        InformSubscriber,
        ResolutionReason
      );

      if (ticketResult.toLowerCase().includes('error')) {
        await addEvent({
          status: 'error',
          total: MSISDNs.length,
          processed: i + 1,
          current: MSISDN,
          message: ticketResult,
          timestamp: new Date().toISOString()
        });
      } else {
        await addEvent({
          status: 'processed',
          total: MSISDNs.length,
          processed: i + 1,
          current: MSISDN,
          message: ticketResult,
          timestamp: new Date().toISOString()
        });
      }


      session.processed = i + 1;
      await updateSession(sessionId, { processed: i + 1 });

      await addEvent({
        status: 'processed',
        total: MSISDNs.length,
        processed: i + 1,
        current: MSISDN,
        message: `MSISDN ${MSISDN} processed (${i + 1}/${MSISDNs.length})`,
        timestamp: new Date().toISOString()
      });

      if (i < MSISDNs.length - 1 && !session.cancelled) {
        console.debug(`[${requestId}]`, `wait 5 seconds`);

        await addEvent({
          status: 'waiting',
          total: MSISDNs.length,
          processed: i + 1,
          message: 'Waiting 5 seconds before processing next MSISDN',
          timestamp: new Date().toISOString()
        });

        const startTime = Date.now();
        const endTime = startTime + 5000;

        while (Date.now() < endTime && !session.cancelled) {
          await sleep(500);
        }
      }
    }

    if (!session.cancelled && !session.paused) {
      console.debug(`[${requestId}]`, `Completing session ${sessionId} with ${session.processed}/${MSISDNs.length} MSISDN processed`);

      session.status = 'completed';
      await updateSession(sessionId, { status: 'completed' });
      await new Promise(resolve => setTimeout(resolve, 500));

      const completionEvent = {
        status: 'completed',
        total: MSISDNs.length,
        processed: session.processed,
        message: 'All MSISDN processed successfully',
        timestamp: new Date().toISOString()
      };

      console.debug(`[${requestId}]`, `Sending completion event for session ${sessionId}:`, JSON.stringify(completionEvent));

      await addEvent(completionEvent);
      await new Promise(resolve => setTimeout(resolve, 1000));

      const finalEvent = {
        status: 'completed',
        total: MSISDNs.length,
        processed: session.processed,
        message: 'Process completed successfully',
        timestamp: new Date().toISOString()
      };

      console.debug(`[${requestId}]`, `Sending final confirmation event for session ${sessionId}:`, JSON.stringify(finalEvent));

      session.events.push(finalEvent);

      const success = await addSessionEvent(sessionId, finalEvent);
      if (!success) {
        console.warn(`[${requestId}]`, `Failed to add final event to database for session ${sessionId}`);
      }

      const clients = recoveryClients.get(sessionId) || new Set();

      console.debug(`[${requestId}]`, `Notifying ${clients.size} clients about final completion for session ${sessionId}`);

      if (clients.size > 0) {
        const eventCopy = JSON.parse(JSON.stringify(finalEvent));

        await Promise.all(
          Array.from(clients).map(async (client) => {
            if (!client.writableEnded) {
              try {
                const message = `data: ${JSON.stringify(eventCopy)}\n\n`;

                client.write(message);
                if (typeof client.flush === 'function') {
                  client.flush();
                }

                console.debug(`[${requestId}]`, `Successfully sent final event to client for session ${sessionId}`);
              } catch (err) {
                console.error(`[${requestId}]`, `Error sending final event to client for session ${sessionId}:`, err);
              }
            } else {
              console.debug(`[${requestId}]`, `Client for session ${sessionId} is no longer writable`);
            }
          })
        );
      }

      console.info(`[${requestId}]`, `Session ${sessionId} completed successfully with ${session.processed}/${MSISDNs.length} MSISDN processed`);

      if (recoveryClients.has(sessionId) && recoveryClients.get(sessionId).size === 0) {
        console.debug(`[${requestId}]`, `Cleaning up completed session ${sessionId} from activeClients map`);
        recoveryClients.delete(sessionId);
      }
    } else if (session.paused) {
      console.debug(`[${requestId}]`, `Session ${sessionId} is paused`);
    } else if (session.cancelled) {
      console.debug(`[${requestId}]`, `Session ${sessionId} was cancelled`);

      if (recoveryClients.has(sessionId) && recoveryClients.get(sessionId).size === 0) {
        console.debug(`[${requestId}]`, `Cleaning up cancelled session ${sessionId} from activeClients map`);
        recoveryClients.delete(sessionId);
      }
    }
  } catch (error) {
    console.error(`[${requestId}]`, 'Error during processing:', error);

    session.status = 'error';
    await updateSession(sessionId, { status: 'error' });
    await addEvent({
      status: 'error',
      message: `Error during processing: ${error.message}`,
      timestamp: new Date().toISOString()
    });

    if (recoveryClients.has(sessionId) && recoveryClients.get(sessionId).size === 0) {
      console.debug(`[${requestId}]`, `Cleaning up error session ${sessionId} from activeClients map`);
      recoveryClients.delete(sessionId);
    }
  }
}
