import passport from 'passport';
import { Strategy as LocalStrategy } from 'passport-local';
import { findUserByUsername, findUserById, verifyPassword } from '../db/index.js';
import console from '../lib/logging.js';

/**
 * Configures Passport.js authentication strategies and serialization.
 *
 * @returns {Object} Configured passport instance
 */
function configurePassport() {
  passport.use(new LocalStrategy(
    /**
     * Local strategy verification callback for Passport.js.
     *
     * @param {string} username - The username submitted by the user
     * @param {string} password - The password submitted by the user
     * @param {Function} done - Passport callback to return authentication result
     * @returns {Promise<void>} Promise that resolves when authentication is complete
     */
    async function(username, password, done) {
      try {
        const user = await findUserByUsername(username);

        if (!user) {
          return done(null, false, { message: 'Неправильний логін або пароль' });
        }

        const isValid = await verifyPassword(password, user.password);

        if (!isValid) {
          return done(null, false, { message: 'Неправильний логін або пароль' });
        }

        return done(null, user);
      } catch (err) {
        console.error('Error during authentication:', err);
        return done(err);
      }
    }
  ));

  /**
   * Serializes user object to store in the session.
   *
   * @param {Object} user - User object to serialize
   * @param {Function} done - Callback to pass serialized user data
   */
  passport.serializeUser(function(user, done) {
    done(null, user.id);
  });

  /**
   * Deserializes user from session data to user object.
   *
   * @param {number|string} id - User ID from session
   * @param {Function} done - Callback to pass deserialized user object
   * @returns {Promise<void>} Promise that resolves when deserialization is complete
   */
  passport.deserializeUser(async function(id, done) {
    try {
      const user = await findUserById(id);
      done(null, user);
    } catch (err) {
      console.error('Error deserializing user:', err);
      done(err);
    }
  });

  return passport;
}

export { configurePassport };
