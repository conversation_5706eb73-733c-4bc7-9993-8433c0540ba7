import console from '../lib/logging.js';

/**
 * Middleware to check if a user is authenticated.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {void} Calls next() if authenticated or returns 401 response if not
 */
function isAuthenticated(req, res, next) {
  if (req.isAuthenticated()) {
    return next();
  }

  console.debug(`[${req.requestId}] Unauthorized access attempt to ${req.originalUrl}`);

  res.status(401).json({
    status: 'error',
    message: 'Для доступу до цієї сторінки необхідно авторизуватись. Будь ласка, увійдіть в систему.'
  });
}

export { isAuthenticated };
