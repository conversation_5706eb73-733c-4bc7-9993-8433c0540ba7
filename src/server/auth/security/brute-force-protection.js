import passport from 'passport';
import { updateUserLockStatus, isUserLocked, incrementFailedLogins, resetFailedLoginAttempts } from '../../db/users.js';
import console from '../../lib/logging.js';

const checkAccountLockout = async (req, res, next) => {
  const { username } = req.body;
  
  if (!username) {
    return next();
  }

  try {
    const isLocked = await isUserLocked(username);
    if (isLocked) {
      console.warn(`[${req.requestId}] Login attempt for locked account: ${username}`);
      return res.status(403).json({
        status: 'error',
        message: 'Обліковий запис заблоковано через занадто багато невдалих спроб входу. Спробуйте пізніше або скиньте пароль.'
      });
    }
    return next();
  } catch (err) {
    console.error(`[${req.requestId}] Error checking account lock status:`, err);
    return next();
  }
};

const trackLoginAttempt = () => {
  return function(req, res, next) {
    passport.authenticate('local', function(err, user, info) {
      if (!req || !req.body || !req.body.username) {
        return next(err);
      }
      
      const { username } = req.body;
      const requestId = req.requestId || 'unknown';
      
      (async () => {
        try {
          if (!user) {
            // Failed login attempt
            const { lockoutThreshold, isLocked } = await incrementFailedLogins(username);
            
            if (isLocked) {
              console.warn(`[${requestId}] Account locked after multiple failed attempts: ${username}`);
              return res.status(403).json({
                status: 'error',
                message: 'Обліковий запис заблоковано через занадто багато невдалих спроб входу. Спробуйте пізніше або скиньте пароль.'
              });
            }
            
            if (lockoutThreshold > 1) {
              console.warn(`[${requestId}] Failed login attempt ${lockoutThreshold}/5 for: ${username}`);
            }
            
            return res.status(401).json({
              status: 'error',
              message: info?.message || 'Неправильний логін або пароль'
            });
          }
          
          await resetFailedLoginAttempts(username);
          
          req.login(user, (err) => {
            if (err) {
              console.error(`[${requestId}]`, 'Error during login:', err);
              return res.status(500).json({
                status: 'error',
                message: 'Виникла помилка при вході в систему. Спробуйте пізніше.'
              });
            }
  
            const userInfo = {
              id: user.id,
              username: user.username,
              email: user.email,
              createdAt: user.created_at
            };
  
            return res.status(200).json({
              status: 'success',
              message: 'Authentication successful',
              user: userInfo
            });
          });
          
        } catch (error) {
          console.error(`[${requestId}] Error tracking login attempt:`, error);
          return res.status(500).json({
            status: 'error',
            message: 'Виникла помилка при вході в систему. Спробуйте пізніше.'
          });
        }
      })();
    })(req, res, next);
  };
};

export { checkAccountLockout, trackLoginAttempt };