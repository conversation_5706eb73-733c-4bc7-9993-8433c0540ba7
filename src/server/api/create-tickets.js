import express from 'express';
import console from '../lib/logging.js';
import crypto from 'crypto';
import { processTicketsAsync } from '../services/process-tickets.js';
import { createSession, getSession, getAllSessions, updateSession, addSessionEvent, getActiveSessionsForUser } from '../db/index.js';
import { activeClients } from '../services/recovery.js';
import { isAuthenticated } from '../auth/auth-middleware.js';

/**
 * Generates a random hexadecimal session ID.
 *
 * @returns {string} A random 32-character hexadecimal string
 */
const generateSessionId = () => crypto.randomBytes(16).toString('hex');

const router = express.Router();

/**
 * Creates a new ticket processing session and starts the async processing.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with session ID and SSE URL
 */
router.post('/cm-echo/api/create-tickets', isAuthenticated, async (req, res) => {
  const sessionId = generateSessionId();
  const requestId = req.requestId;

  const body = req.body;
  const MSISDNs = body.MSISDN.split(',');
  const subscriptionIds = body.SubscriptionId && body.SubscriptionId.trim() !== ''
    ? body.SubscriptionId.split(',')
    : Array(MSISDNs.length).fill('');
  const {
    CategoryName,
    VirtualTeamName,
    ReactionName,
    ComplainDetails,
    Title,
    ResolutionSummary,
    TickerStatus,
    NotificationType,
    InformSubscriber,
    ResolutionReason
  } = body;

  const session = {
    id: sessionId,
    requestId,
    data: {
      MSISDNs,
      subscriptionIds,
      CategoryName,
      VirtualTeamName,
      ReactionName,
      ComplainDetails,
      Title,
      ResolutionSummary,
      TickerStatus,
      NotificationType,
      InformSubscriber,
      ResolutionReason
    },
    events: [],
    status: 'created',
    total: MSISDNs.length,
    processed: 0,
    clients: new Set(),
    cancelled: false,
    paused: false,
    userId: req.user ? req.user.id : null,
    username: req.user ? req.user.username : null
  };

  console.debug(`[${requestId}]`, `Creating session for user: ${session.username} (ID: ${session.userId})`);

  const created = await createSession(session);
  if (!created) {
    return res.status(500).json({
      status: 'error',
      message: 'Failed to create session. Please try again.'
    });
  }

  activeClients.set(sessionId, session.clients);

  processTicketsAsync(session);

  return res.status(200).json({
    status: 'processing_started',
    sessionId,
    message: 'Processing started. Connect to the SSE endpoint to monitor progress.',
    sseUrl: `/cm-echo/api/create-tickets/events/${sessionId}`
  });
});

/**
 * Checks if the current user is the owner of the session.
 *
 * @param {Object} session - Session object from database
 * @param {Object} req - Express request object
 * @returns {boolean} True if user is the session owner or if session has no owner
 */
const isSessionOwner = (session, req) => {
  if (!session.user_id) return true;

  return req.user && req.user.id === session.user_id;
};

/**
 * Sets up Server-Sent Events (SSE) connection for real-time session updates.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} SSE connection or error response
 */
router.get('/cm-echo/api/create-tickets/events/:sessionId', isAuthenticated, async (req, res) => {
  const sessionId = req.params.sessionId;

  const session = await getSession(sessionId);

  if (!session) {
    return res.status(404).json({
      status: 'error',
      message: 'Session not found. The processing might have completed or the session ID is invalid.'
    });
  }

  if (!isSessionOwner(session, req)) {
    console.debug(`[${req.requestId}]`, `User ${req.user.username} (ID: ${req.user.id}) attempted to access session ${sessionId} owned by ${session.username} (ID: ${session.user_id})`);
    return res.status(403).json({
      status: 'error',
      message: 'You do not have permission to access this session.'
    });
  }

  if (!activeClients.has(sessionId)) {
    activeClients.set(sessionId, new Set());
  }
  const clients = activeClients.get(sessionId);

  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');

  clients.add(res);
  session.clients = clients;

  console.debug(`[${req.requestId}]`, `Client connected to session ${sessionId}`);


  if (Array.isArray(session.events) && session.events.length > 0) {
    console.debug(`[${req.requestId}]`, `Sending ${session.events.length} events to client for session ${sessionId}`);

    const eventsWithTimestamps = session.events.map(event => {
      const updatedEvent = { ...event };

      if (!updatedEvent.timestamp) {
        updatedEvent.timestamp = new Date().toISOString();
      }

      if (typeof updatedEvent.total !== 'number') {
        updatedEvent.total = session.total || 0;
      }

      if (typeof updatedEvent.processed !== 'number') {
        updatedEvent.processed = session.processed || 0;
      }

      return updatedEvent;
    });

    eventsWithTimestamps.forEach((event, index) => {
      setTimeout(() => {
        if (!res.writableEnded) {
          res.write(`data: ${JSON.stringify(event)}\n\n`);
          if (typeof res.flush === 'function') {
            res.flush();
          }
        }
      }, index * 10);
    });
  } else {
    console.debug(`[${req.requestId}]`, `No events to send for session ${sessionId}`);
  }

  req.on('close', async () => {
    console.debug(`[${req.requestId}]`, `Client disconnected from session ${sessionId}`);
    clients.delete(res);

    setTimeout(() => {
      if (clients.size === 0) {
        console.debug(`[${req.requestId}]`, `No clients connected to session ${sessionId} after timeout, removing from activeClients map`);
        activeClients.delete(sessionId);
      }
    }, 5000);
  });
});

/**
 * Retrieves all sessions from the database.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with list of all sessions
 */
router.get('/cm-echo/api/sessions', isAuthenticated, async (req, res) => {
  try {
    const sessions = await getAllSessions();

    const formattedSessions = sessions.map(session => {
      const isOwner = isSessionOwner(session, req);

      return {
        sessionId: session.id,
        status: session.status,
        total: session.total,
        processed: session.processed,
        createdAt: session.created_at,
        updatedAt: session.updated_at,
        events: session.events,
        userId: session.user_id,
        username: session.username,
        isOwner: isOwner,
        paused: session.paused
      };
    });

    return res.status(200).json({
      status: 'success',
      sessions: formattedSessions
    });
  } catch (error) {
    console.error(`[${req.requestId}]`, 'Error getting sessions:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to get sessions. Please try again.'
    });
  }
});

/**
 * Retrieves active sessions for the current authenticated user.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with list of user's active sessions
 */
router.get('/cm-echo/api/sessions/active', isAuthenticated, async (req, res) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        status: 'error',
        message: 'User not authenticated'
      });
    }

    const sessions = await getActiveSessionsForUser(req.user.id);

    const formattedSessions = sessions.map(session => ({
      sessionId: session.id,
      status: session.status,
      total: session.total,
      processed: session.processed,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      events: session.events,
      userId: session.userId,
      username: session.username,
      isOwner: true,
      paused: session.paused
    }));

    return res.status(200).json({
      status: 'success',
      sessions: formattedSessions
    });
  } catch (error) {
    console.error(`[${req.requestId}]`, 'Error getting active sessions for user:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Failed to get active sessions. Please try again.'
    });
  }
});

/**
 * Retrieves details for a specific session with optional pagination for events.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with session details or just events if eventsOnly=true
 */
router.get('/cm-echo/api/sessions/:sessionId', isAuthenticated, async (req, res) => {
  const sessionId = req.params.sessionId;

  const limit = parseInt(req.query.limit) || 0;
  const offset = parseInt(req.query.offset) || 0;
  const eventsOnly = req.query.eventsOnly === 'true';

  const session = await getSession(sessionId);

  if (!session) {
    return res.status(404).json({
      status: 'error',
      message: 'Session not found. The session ID might be invalid.'
    });
  }

  const isOwner = isSessionOwner(session, req);

  const totalEvents = session.events.length;

  let paginatedEvents = session.events;
  if (limit > 0) {
    paginatedEvents = session.events.slice(offset, offset + limit);
  }

  if (eventsOnly) {
    return res.status(200).json({
      status: 'success',
      events: paginatedEvents,
      totalEvents: totalEvents,
      offset: offset,
      limit: limit
    });
  }

  const formattedSession = {
    sessionId: session.id,
    status: session.status,
    total: session.total,
    processed: session.processed,
    createdAt: session.created_at,
    updatedAt: session.updated_at,
    events: paginatedEvents,
    totalEvents: totalEvents,
    data: session.data,
    userId: session.user_id,
    username: session.username,
    isOwner: isOwner,
    paused: session.paused
  };

  return res.status(200).json({
    status: 'success',
    session: formattedSession
  });
});

/**
 * Retrieves the current status of a specific ticket creation session.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with session status information
 */
router.get('/cm-echo/api/create-tickets/status/:sessionId', isAuthenticated, async (req, res) => {
  const sessionId = req.params.sessionId;

  const session = await getSession(sessionId);

  if (!session) {
    return res.status(404).json({
      status: 'error',
      message: 'Session not found. The processing might have completed or the session ID is invalid.'
    });
  }

  if (!isSessionOwner(session, req)) {
    console.debug(`[${req.requestId}]`, `User ${req.user.username} (ID: ${req.user.id}) attempted to access session ${sessionId} owned by ${session.username} (ID: ${session.user_id})`);
    return res.status(403).json({
      status: 'error',
      message: 'You do not have permission to access this session.'
    });
  }

  return res.status(200).json({
    sessionId: session.id,
    status: session.status,
    total: session.total,
    processed: session.processed,
    lastEvent: session.events.length > 0 ? session.events[session.events.length - 1] : null,
    userId: session.user_id,
    username: session.username,
    paused: session.paused
  });
});

/**
 * Cancels an ongoing ticket creation session.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with cancellation status
 */
router.delete('/cm-echo/api/create-tickets/:sessionId', isAuthenticated, async (req, res) => {
  const sessionId = req.params.sessionId;

  const session = await getSession(sessionId);

  if (!session) {
    return res.status(404).json({
      status: 'error',
      message: 'Session not found. The processing might have completed or the session ID is invalid.'
    });
  }

  if (!isSessionOwner(session, req)) {
    console.debug(`[${req.requestId}]`, `User ${req.user.username} (ID: ${req.user.id}) attempted to cancel session ${sessionId} owned by ${session.username} (ID: ${session.user_id})`);
    return res.status(403).json({
      status: 'error',
      message: 'You do not have permission to cancel this session.'
    });
  }

  await updateSession(sessionId, { cancelled: true, status: 'cancelled' });

  session.cancelled = true;
  session.status = 'cancelled';

  console.debug(`[${req.requestId}]`, `Session ${sessionId} updated for cancel: status=${session.status}, cancelled=${session.cancelled}`);

  const cancelEvent = {
    status: 'cancelled',
    total: session.total,
    processed: session.processed,
    message: 'Processing cancelled by user request',
    timestamp: new Date().toISOString()
  };

  console.debug(`[${req.requestId}]`, `Adding cancel event to session ${sessionId}:`, JSON.stringify(cancelEvent));

  const success = await addSessionEvent(sessionId, cancelEvent);
  if (!success) {
    console.warn(`[${req.requestId}]`, `Failed to add cancel event to database for session ${sessionId}`);
  }

  const clients = activeClients.get(sessionId) || new Set();
  console.debug(`[${req.requestId}]`, `Notifying ${clients.size} clients about cancellation for session ${sessionId}`);

  clients.forEach(client => {
    if (!client.writableEnded) {
      try {
        client.write(`data: ${JSON.stringify(cancelEvent)}\n\n`);
        if (typeof client.flush === 'function') {
          client.flush();
        }
      } catch (err) {
        console.error(`[${req.requestId}]`, `Error sending cancel event to client for session ${sessionId}:`, err);
      }
    }
  });

  return res.status(200).json({
    status: 'cancelled',
    message: 'Processing cancelled successfully'
  });
});

/**
 * Pauses an ongoing ticket creation session.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with pause status
 */
router.post('/cm-echo/api/create-tickets/pause/:sessionId', isAuthenticated, async (req, res) => {
  const sessionId = req.params.sessionId;

  const session = await getSession(sessionId);

  if (!session) {
    return res.status(404).json({
      status: 'error',
      message: 'Session not found. The processing might have completed or the session ID is invalid.'
    });
  }

  if (!isSessionOwner(session, req)) {
    console.debug(`[${req.requestId}]`, `User ${req.user.username} (ID: ${req.user.id}) attempted to pause session ${sessionId} owned by ${session.username} (ID: ${session.user_id})`);
    return res.status(403).json({
      status: 'error',
      message: 'You do not have permission to pause this session.'
    });
  }

  if (['completed', 'failed', 'error', 'cancelled'].includes(session.status)) {
    return res.status(400).json({
      status: 'error',
      message: `Cannot pause a session with status '${session.status}'`
    });
  }

  await updateSession(sessionId, { paused: true, status: 'paused' });

  session.paused = true;
  session.status = 'paused';

  console.debug(`[${req.requestId}]`, `Session ${sessionId} updated for pause: status=${session.status}, paused=${session.paused}`);

  const pauseEvent = {
    status: 'paused',
    total: session.total,
    processed: session.processed,
    message: 'Processing paused by user request',
    timestamp: new Date().toISOString()
  };

  console.debug(`[${req.requestId}]`, `Adding pause event to session ${sessionId}:`, JSON.stringify(pauseEvent));

  const success = await addSessionEvent(sessionId, pauseEvent);
  if (!success) {
    console.warn(`[${req.requestId}]`, `Failed to add pause event to database for session ${sessionId}`);
  }

  const clients = activeClients.get(sessionId) || new Set();
  console.debug(`[${req.requestId}]`, `Notifying ${clients.size} clients about pause for session ${sessionId}`);

  clients.forEach(client => {
    if (!client.writableEnded) {
      try {
        client.write(`data: ${JSON.stringify(pauseEvent)}\n\n`);
        if (typeof client.flush === 'function') {
          client.flush();
        }
      } catch (err) {
        console.error(`[${req.requestId}]`, `Error sending pause event to client for session ${sessionId}:`, err);
      }
    }
  });

  return res.status(200).json({
    status: 'paused',
    message: 'Processing paused successfully'
  });
});

/**
 * Resumes a paused ticket creation session.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with resume status
 */
router.post('/cm-echo/api/create-tickets/resume/:sessionId', isAuthenticated, async (req, res) => {
  const sessionId = req.params.sessionId;

  const session = await getSession(sessionId);

  if (!session) {
    return res.status(404).json({
      status: 'error',
      message: 'Session not found. The processing might have completed or the session ID is invalid.'
    });
  }

  if (!isSessionOwner(session, req)) {
    console.debug(`[${req.requestId}]`, `User ${req.user.username} (ID: ${req.user.id}) attempted to resume session ${sessionId} owned by ${session.username} (ID: ${session.user_id})`);
    return res.status(403).json({
      status: 'error',
      message: 'You do not have permission to resume this session.'
    });
  }

  console.debug(`[${req.requestId}]`, `Resume request for session ${sessionId}:`, {
    status: session.status,
    paused: session.paused,
    cancelled: session.cancelled,
    processed: session.processed,
    total: session.total
  });

  if (!session.paused && session.status !== 'paused') {
    console.debug(`[${req.requestId}]`, `Cannot resume session ${sessionId} with status '${session.status}' and paused=${session.paused}`);
    return res.status(400).json({
      status: 'error',
      message: `Cannot resume a session with status '${session.status}' and paused=${session.paused}`
    });
  }

  console.debug(`[${req.requestId}]`, `Session ${sessionId} can be resumed: status='${session.status}', paused=${session.paused}`);

  if (session.status === 'completed' && session.paused) {
    console.debug(`[${req.requestId}]`, `Session ${sessionId} was marked as completed but is still paused, resetting status`);
  }

  if (session.status === 'cancelled' && session.paused) {
    console.debug(`[${req.requestId}]`, `Resuming a session that was marked as cancelled but was paused: ${sessionId}`);
  }

  if (session.status === 'cancelled' && session.paused) {
    await updateSession(sessionId, { paused: false, cancelled: false, status: 'resumed' });

    session.paused = false;
    session.cancelled = false;
    session.status = 'resumed';
  } else if (session.status === 'completed' && session.paused) {
    await updateSession(sessionId, { paused: false, status: 'resumed' });

    session.paused = false;
    session.status = 'resumed';
  } else {
    await updateSession(sessionId, { paused: false, status: 'resumed' });

    session.paused = false;
    session.status = 'resumed';
  }

  console.debug(`[${req.requestId}]`, `Session ${sessionId} updated for resume: status=${session.status}, paused=${session.paused}, cancelled=${session.cancelled}`);

  const resumeEvent = {
    status: 'resumed',
    total: session.total,
    processed: session.processed,
    message: 'Processing resumed by user request',
    timestamp: new Date().toISOString()
  };

  console.debug(`[${req.requestId}]`, `Adding resume event to session ${sessionId}:`, JSON.stringify(resumeEvent));

  const success = await addSessionEvent(sessionId, resumeEvent);
  if (!success) {
    console.warn(`[${req.requestId}]`, `Failed to add resume event to database for session ${sessionId}`);
  }

  const clients = activeClients.get(sessionId) || new Set();
  console.debug(`[${req.requestId}]`, `Notifying ${clients.size} clients about resume for session ${sessionId}`);

  clients.forEach(client => {
    if (!client.writableEnded) {
      try {
        client.write(`data: ${JSON.stringify(resumeEvent)}\n\n`);
        if (typeof client.flush === 'function') {
          client.flush();
        }
      } catch (err) {
        console.error(`[${req.requestId}]`, `Error sending resume event to client for session ${sessionId}:`, err);
      }
    }
  });


  session.clients = activeClients.get(sessionId) || new Set();

  processTicketsAsync(session);

  return res.status(200).json({
    status: 'resumed',
    message: 'Processing resumed successfully'
  });
});


export default router;
