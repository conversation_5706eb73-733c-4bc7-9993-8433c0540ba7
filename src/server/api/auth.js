import express from 'express';
import passport from 'passport';
import rateLimit from 'express-rate-limit';
import console from '../lib/logging.js';
import sendEmail from '../services/email.js';
import { findUserByEmail, changeUserPassword, createPasswordResetToken, verifyPasswordResetToken, updateUserPassword, deletePasswordResetToken, getAllUsers, updateUserLockStatus } from '../db/index.js';
import { isAuthenticated } from '../auth/auth-middleware.js';
import { checkAccountLockout, trackLoginAttempt } from '../auth/security/brute-force-protection.js';

const router = express.Router();

const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 20,
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    console.warn(`[${req.requestId}] Login rate limit exceeded for IP: ${req.ip}`);
    return res.status(429).json({
      status: 'error',
      message: 'Забагато спроб входу. Спробуйте пізніше.'
    });
  }
});

/**
 * Handles user authentication via username and password.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Object} JSON response with authentication status and user info on success
 */
router.post('/cm-echo/api/auth/login',
  express.json(),
  loginLimiter,
  checkAccountLockout,
  trackLoginAttempt(),
  (req, res, next) => {
    passport.authenticate('local', (err, user, info) => {
      if (err) {
        console.error(`[${req.requestId}]`, 'Error during authentication:', err);
        return res.status(500).json({
          status: 'error',
          message: 'Помилка при авторизації'
        });
      }

      if (!user) {
        return res.status(401).json({
          status: 'error',
          message: info.message || 'Неправильний логін або пароль'
        });
      }

      req.logIn(user, (err) => {
        if (err) {
          console.error(`[${req.requestId}]`, 'Error during login:', err);
          return res.status(500).json({
            status: 'error',
            message: 'Помилка при вході в систему'
          });
        }

        return res.status(200).json({
          status: 'success',
          message: 'Успішний вхід в систему',
          user: {
            id: user.id,
            username: user.username,
            email: user.email
          }
        });
      });
    })(req, res, next);
  }
);

/**
 * Handles user logout by destroying the session.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with logout status
 */
router.post('/cm-echo/api/auth/logout', (req, res) => {
  if (!req.isAuthenticated()) {
    console.debug(`[${req.requestId}] User not authenticated, already logged out`);
    return res.status(200).json({
      status: 'success',
      message: 'Ви вже вийшли з системи'
    });
  }

  req.logout(function(err) {
    if (err) {
      console.error(`[${req.requestId}]`, 'Error during logout:', err);
      return res.status(500).json({
        status: 'error',
        message: 'Помилка при виході з системи. Спробуйте пізніше.'
      });
    }

    return res.status(200).json({
      status: 'success',
      message: 'Успішний вихід із системи'
    });
  });
});

/**
 * Retrieves the current authenticated user's information.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with user information if authenticated
 */
router.get('/cm-echo/api/auth/me', (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({
      status: 'error',
      message: 'Для доступу до цієї інформації потрібно авторизуватись. Будь ласка, увійдіть в систему.'
    });
  }

  const userInfo = {
    id: req.user.id,
    username: req.user.username,
    email: req.user.email,
    createdAt: req.user.created_at
  };

  return res.status(200).json({
    status: 'success',
    user: userInfo
  });
});

/**
 * Handles user signup requests by sending an email to the admin.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response indicating the request was sent
 */
router.post('/cm-echo/api/auth/signup', express.json(), async (req, res) => {
  const body = req.body;

  await sendEmail(process.env.ADMIN_EMAIL, 'NEW USER REQUEST', `Запит від користувача: <b>${body.email}</b>  на створення облікового запису в системі <b>cm-echo</b>.<br><br>Повідомлення від користувача: ${body.text}`);

  return res.status(200).json({
    status: 'success',
    message: 'Запит успішно надіслано'
  });
});

/**
 * Handles password change for authenticated users.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with password change status
 */
router.post('/cm-echo/api/auth/change-password', isAuthenticated, express.json(), async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user.id;

  if (!currentPassword || !newPassword) {
    return res.status(400).json({
      status: 'error',
      message: 'Поточний пароль та новий пароль є обов\'язковими'
    });
  }

  if (newPassword.length < 8) {
    return res.status(400).json({
      status: 'error',
      message: 'Новий пароль повинен містити щонайменше 8 символів'
    });
  }

  try {
    const result = await changeUserPassword(userId, currentPassword, newPassword);

    if (result === 'same-password') {
      return res.status(400).json({
        status: 'error',
        message: 'Новий пароль співпадає з поточним. Будь ласка, виберіть інший пароль.'
      });
    }

    if (!result) {
      return res.status(400).json({
        status: 'error',
        message: 'Неправильний поточний пароль'
      });
    }

    return res.status(200).json({
      status: 'success',
      message: 'Пароль успішно змінено'
    });
  } catch (err) {
    console.error(`[${req.requestId}]`, 'Error changing password:', err);
    return res.status(500).json({
      status: 'error',
      message: 'Помилка при зміні паролю'
    });
  }
});

/**
 * Handles password reset requests by sending a reset link via email.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response indicating the reset email was sent (if user exists)
 */
router.post('/cm-echo/api/auth/request-reset', express.json(), async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      status: 'error',
      message: 'Email є обов\'язковим'
    });
  }

  try {
    const user = await findUserByEmail(email);

    if (!user) {
      console.debug(`[${req.requestId}]`, `Password reset requested for non-existent email: ${email}`);
      return res.status(200).json({
        status: 'success',
        message: 'Якщо обліковий запис з цією електронною адресою існує, інструкції щодо скидання паролю будуть надіслані на вказану адресу'
      });
    }

    const tokenData = await createPasswordResetToken(user.id);

    if (!tokenData) {
      console.error(`[${req.requestId}]`, `Failed to create password reset token for user: ${user.username}`);
      return res.status(500).json({
        status: 'error',
        message: 'Помилка при створенні токена скидання паролю'
      });
    }

    const resetUrl = `${req.protocol}://${req.get('host')}/cm-echo/reset-password?token=${tokenData.token}`;

    const emailSubject = 'Скидання паролю в системі CM Echo';
    const emailBody = `
      <h2>Скидання паролю в системі CM Echo</h2>
      <p>Ви отримали цей лист, оскільки було зроблено запит на скидання паролю для вашого облікового запису.</p>
      <p>Будь ласка, перейдіть за посиланням нижче, щоб скинути пароль:</p>
      <p><a href="${resetUrl}">${resetUrl}</a></p>
      <p>Це посилання дійсне протягом 1 години.</p>
      <p>Якщо ви не робили запит на скидання паролю, проігноруйте цей лист.</p>
    `;

    const emailSent = await sendEmail(user.email, emailSubject, emailBody);

    if (!emailSent) {
      console.error(`[${req.requestId}]`, `Failed to send password reset email to: ${user.email}`);
      return res.status(500).json({
        status: 'error',
        message: 'Помилка при відправці електронного листа'
      });
    }

    return res.status(200).json({
      status: 'success',
      message: 'Якщо обліковий запис з цією електронною адресою існує, інструкції щодо скидання паролю будуть надіслані на вказану адресу'
    });
  } catch (err) {
    console.error(`[${req.requestId}]`, 'Error requesting password reset:', err);
    return res.status(500).json({
      status: 'error',
      message: 'Помилка при запиті на скидання паролю'
    });
  }
});

/**
 * Resets a user's password using a valid reset token.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with password reset status
 */
router.post('/cm-echo/api/auth/reset-password', express.json(), async (req, res) => {
  const { token, newPassword } = req.body;

  if (!token || !newPassword) {
    return res.status(400).json({
      status: 'error',
      message: 'Токен та новий пароль є обов\'язковими'
    });
  }

  if (newPassword.length < 8) {
    return res.status(400).json({
      status: 'error',
      message: 'Новий пароль повинен містити щонайменше 8 символів'
    });
  }

  try {
    const tokenData = await verifyPasswordResetToken(token);

    if (!tokenData) {
      return res.status(400).json({
        status: 'error',
        message: 'Недійсний або прострочений токен скидання паролю'
      });
    }

    const success = await updateUserPassword(tokenData.userId, newPassword);

    if (!success) {
      return res.status(500).json({
        status: 'error',
        message: 'Помилка при оновленні паролю'
      });
    }

    await deletePasswordResetToken(token);

    return res.status(200).json({
      status: 'success',
      message: 'Пароль успішно скинуто. Тепер ви можете увійти з новим паролем.'
    });
  } catch (err) {
    console.error(`[${req.requestId}]`, 'Error resetting password:', err);
    return res.status(500).json({
      status: 'error',
      message: 'Помилка при скиданні паролю'
    });
  }
});

/**
 * Retrieves a list of all users in the system.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with list of users
 */
router.get('/cm-echo/api/users', isAuthenticated, async (req, res) => {
  try {
    const users = await getAllUsers();

    const formattedUsers = users.map(user => ({
      id: user.id,
      username: user.username
    }));

    return res.status(200).json({
      status: 'success',
      users: formattedUsers
    });
  } catch (err) {
    console.error(`[${req.requestId}]`, 'Error getting users:', err);
    return res.status(500).json({
      status: 'error',
      message: 'Помилка при отриманні списку користувачів'
    });
  }
});

/**
 * Admin endpoint to manage account lockout status.
 * Allows administrators to lock or unlock user accounts.
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with account lockout status update result
 */
router.post('/cm-echo/api/auth/manage-lockout', isAuthenticated, express.json(), async (req, res) => {
  if (!req.user || req.user.username !== process.env.ADMIN_USERNAME) {
    return res.status(403).json({
      status: 'error',
      message: 'Не достатньо прав для виконання цієї операції'
    });
  }

  const { username, isLocked } = req.body;

  if (!username || isLocked === undefined) {
    return res.status(400).json({
      status: 'error',
      message: 'Ім\'я користувача та статус блокування є обов\'язковими'
    });
  }

  try {
    const success = await updateUserLockStatus(username, isLocked);

    if (!success) {
      return res.status(404).json({
        status: 'error',
        message: 'Користувача не знайдено'
      });
    }

    return res.status(200).json({
      status: 'success',
      message: isLocked
        ? 'Обліковий запис користувача заблоковано'
        : 'Обліковий запис користувача розблоковано'
    });
  } catch (err) {
    console.error(`[${req.requestId}]`, 'Error managing account lockout:', err);
    return res.status(500).json({
      status: 'error',
      message: 'Помилка при керуванні статусом облікового запису'
    });
  }
});

export default router;
