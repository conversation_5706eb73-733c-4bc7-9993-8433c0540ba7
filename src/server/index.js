import * as http from 'http';
import express from 'express';
import cors from 'cors';
import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import session from 'express-session';
import rateLimit from 'express-rate-limit';
import MariaDBStore from './lib/mariadb-session-store.js';
import { createTickets, auth } from './api/index.js';
import console from './lib/logging.js';
import { initDb } from './db/index.js';
import { recoverIncompleteSessions, cleanupExpiredSessions } from './services/recovery.js';
import { configurePassport } from './auth/passport-config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const FRONTEND_DIST_PATH = path.resolve(__dirname, '../../src/frontend/dist');
const FRONTEND_INDEX_PATH = path.resolve(FRONTEND_DIST_PATH, 'index.html');

const DOCS_DIST_PATH = path.resolve(__dirname, '../../src/docs/.vitepress/dist');
const DOCS_INDEX_PATH = path.resolve(DOCS_DIST_PATH, 'index.html');

const frontendDistExists = fs.existsSync(FRONTEND_DIST_PATH);
const frontendIndexExists = fs.existsSync(FRONTEND_INDEX_PATH);

const docsDistExists = fs.existsSync(DOCS_DIST_PATH);
const docsIndexExists = fs.existsSync(DOCS_INDEX_PATH);

if (!frontendDistExists || !frontendIndexExists) {
  console.warn(`Frontend dist directory or index.html not found at ${FRONTEND_DIST_PATH}`);
  console.warn('Make sure to build the frontend before starting the server');
}

if (!docsDistExists || !docsIndexExists) {
  console.warn(`Documentation dist directory or index.html not found at ${DOCS_DIST_PATH}`);
  console.warn('Make sure to build the documentation before starting the server');
}

const app = express();
const httpServer = http.createServer(app);
const httpPort = 8080;
const host = '0.0.0.0';

app.set('trust proxy', ['loopback', 'linklocal', 'uniquelocal']);

const apiLimiter = rateLimit({
  windowMs: 60 * 1000,
  max: 100, 
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    console.warn(`Rate limit exceeded for IP: ${req.ip}`);
    return res.status(429).json({
      status: 'error',
      message: 'Забагато запитів. Спробуйте пізніше.'
    });
  }
});

app.use('/cm-echo/api/', apiLimiter);

app.use(cors({
  origin: ['https://cm.kyivstar.ua', 'https://cca.kyivstar.ua', 'http://localhost:8080'],
  credentials: true
}));

const generateCId = () => crypto.randomBytes(6).toString('hex');

const truncateBody = (body) => {
  if (!body || Object.keys(body).length === 0) return '';

  const logBody = { ...body };

  Object.keys(logBody).forEach(key => {
    if (key.toLowerCase().includes('password')) {
      logBody[key] = '******';
    }
  });

  if (logBody.MSISDN) {
    const msisdnCount = logBody.MSISDN.split(',').length;
    logBody.MSISDN = `${msisdnCount} numbers`;
  }

  if (logBody.SubscriptionId) {
    const subscriptionCount = logBody.SubscriptionId.split(',').length;
    logBody.SubscriptionId = `${subscriptionCount} numbers`;
  }

  return ` ${JSON.stringify(logBody)}`;
};

const logging = (req, res, next) => {
  req.requestId = req.headers['x-request-id'] || generateCId();
  res.on('finish', () => {
    const truncatedBody = truncateBody(req.body);
    console.debug(
      `[${req.requestId}] ${req.method} ${res.statusCode} ${req.protocol} ${req.originalUrl}${truncatedBody}`
    );
  });
  next();
};

const appVersion = () => {
  try {
    const packageData = fs.readFileSync('package.json', 'utf8');
    const packageJson = JSON.parse(packageData);
    return packageJson.version;
  } catch (error) {
    console.error(error.message);
    return null;
  }
};

const version = appVersion();
const currentMode = process.env.MODE || 'PROD';

app.use(logging);
app.use(express.json({ limit: '1mb' }));

app.get('/favicon.ico', (_, res) => {
  return res.status(204).end();
});

app.get('/cm-echo/favicon.ico', (_, res) => {
  const faviconPath = path.join(FRONTEND_DIST_PATH, 'favicon.ico');
  if (fs.existsSync(faviconPath)) {
    return res.sendFile(faviconPath);
  }
  return res.status(204).end();
});

app.get('/cm-echo/version', async (_, res) => {
  return res.status(200).json({ version: version });
});

const sessionStore = new MariaDBStore({
  host: process.env.MARIADB_HOST,
  port: process.env.MARIADB_PORT || 3306,
  user: process.env.MARIADB_USER,
  password: process.env.MARIADB_PASSWORD,
  database: process.env.MARIADB_NAME,
  table: 'auth_sessions',
  ttl: 86400,
  clearInterval: 3600, 
  createTable: true
});

sessionStore.on('error', function (error) {
  console.error('Session store error:', error);
});

app.use(session({
  key: 'cm_echo_session',
  secret: process.env.SESSION_SECRET || 'keyboard cat',
  store: sessionStore,
  resave: false,
  saveUninitialized: false,
  rolling: true,
  proxy: true,
  cookie: {
    maxAge: 86400000,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    path: process.env.COOKIE_PATH || '/',
    sameSite: 'lax'
  }
}));

const passportInstance = configurePassport();
app.use(passportInstance.initialize());
app.use(passportInstance.session());

app.use([auth]);

app.use([createTickets]);

app.use('/cm-echo', express.static(FRONTEND_DIST_PATH));

app.use('/cm-echo/docs', express.static(DOCS_DIST_PATH));

app.get('/cm-echo/documentation', (_, res) => {
  res.redirect('/cm-echo/docs/');
});

app.get('/cm-echo', (req, res) => {
  res.sendFile(FRONTEND_INDEX_PATH, (err) => {
    if (err) {
      console.error(`[${req.requestId}] Error serving index.html:`, err);
      res.status(500).send('Error loading application. Please try again later.');
    }
  });
});

app.get('/cm-echo/docs', (req, res) => {
  res.sendFile(DOCS_INDEX_PATH, (err) => {
    if (err) {
      console.error(`[${req.requestId}] Error serving docs index.html:`, err);
      res.status(500).send('Error loading documentation. Please try again later.');
    }
  });
});

app.get(/^\/cm-echo\/docs\/.+/, (req, res) => {
  const requestedPath = req.path.replace('/cm-echo/docs/', '');
  const fullPath = path.join(DOCS_DIST_PATH, requestedPath);

  if (fs.existsSync(fullPath) && fs.statSync(fullPath).isFile()) {
    return res.sendFile(fullPath);
  }

  res.sendFile(DOCS_INDEX_PATH, (err) => {
    if (err) {
      console.error(`[${req.requestId}] Error serving docs index.html:`, err);
      res.status(500).send('Error loading documentation. Please try again later.');
    }
  });
});

app.get(/^\/cm-echo\/(?!api\/)(?!docs\/).+/, (req, res) => {
  res.sendFile(FRONTEND_INDEX_PATH, (err) => {
    if (err) {
      console.error(`[${req.requestId}] Error serving index.html:`, err);
      res.status(500).send('Error loading application. Please try again later.');
    }
  });
});

app.use(function (req, res, next) {
  if (!req.route) {
    return res.status(404).json({ status: 'error', message: 'method not exists' });
  }
  next();
});

process.on('uncaughtException', error => {
  console.fatal('Uncaught Exception:', error);
});

initDb().then(async dbInitialized => {
  if (!dbInitialized) {
    console.warn('Database initialization failed. Some features may not work correctly.');
  } else {
    try {
      const recoveredCount = await recoverIncompleteSessions();
      if (recoveredCount > 0) {
        console.info(`Recovered ${recoveredCount} incomplete sessions`);
      }
    } catch (error) {
      console.error('Error during session recovery:', error);
    }
  }

  httpServer.listen(httpPort, host, () => {
    console.debug(
      `mode: ${currentMode} | app v${version}`
    );
    console.debug(`API available at: http://localhost:${httpPort}/cm-echo/api/`);
    console.debug(`Frontend available at: http://localhost:${httpPort}/cm-echo/`);
    console.debug(`Documentation available at: http://localhost:${httpPort}/cm-echo/docs/`);

    // Set up periodic cleanup every 15 minutes
    const CLEANUP_INTERVAL = 15 * 60 * 1000; // 15 minutes
    setInterval(async () => {
      try {
        const cleanedCount = await cleanupExpiredSessions();
        if (cleanedCount > 0) {
          console.debug(`Cleaned up ${cleanedCount} expired sessions`);
        }
      } catch (error) {
        console.error('Error during session cleanup:', error);
      }
    }, CLEANUP_INTERVAL);
  });
});
