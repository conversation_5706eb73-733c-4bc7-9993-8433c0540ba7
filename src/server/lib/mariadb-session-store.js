import mysql from 'mysql2/promise';
import session from 'express-session';
import { EventEmitter } from 'events';
import console from './logging.js';

/**
 * MariaDBStore - A MariaDB/MySQL session store for express-session
 *
 * @class
 * @extends session.Store
 */
class MariaDBStore extends session.Store {
  /**
   * Creates a new MariaDBStore instance
   *
   * @param {Object} options - Configuration options
   * @param {string} [options.host='localhost'] - Database host
   * @param {number} [options.port=3306] - Database port
   * @param {string} [options.user='root'] - Database user
   * @param {string} [options.password=''] - Database password
   * @param {string} [options.database='cm-echo'] - Database name
   * @param {number} [options.connectionLimit=10] - Maximum number of connections in the pool
   * @param {string} [options.table='auth_sessions'] - Table name for storing sessions
   * @param {number} [options.ttl=86400] - Session time-to-live in seconds
   * @param {number} [options.clearInterval=3600] - Interval in seconds for clearing expired sessions
   * @param {boolean} [options.createTable=true] - Whether to create the sessions table if it doesn't exist
   */
  constructor(options = {}) {
    super();

    this._emitter = new EventEmitter();
    this._emitter.setMaxListeners(0);

    this.options = {
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: process.env.MARIADB_NAME,
      connectionLimit: 10,

      table: 'auth_sessions',
      ttl: 86400,
      clearInterval: 3600,
      createTable: true,

      ...options
    };

    this.pool = mysql.createPool({
      host: this.options.host,
      port: this.options.port,
      user: this.options.user,
      password: this.options.password,
      database: this.options.database,
      connectionLimit: this.options.connectionLimit,
      waitForConnections: true,
      timezone: 'Z'
    });

    this.initialize();
  }

  /**
   * Initializes the session store by creating the table if needed and starting cleanup
   *
   * @async
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      if (this.options.createTable) {
        await this.createTable();
      }

      if (this.options.clearInterval > 0) {
        this.startCleanup();
      }

      console.debug('MariaDB session store initialized successfully');
    } catch (err) {
      console.error('Error initializing MariaDB session store:', err);
      this._emitter.emit('error', err);
    }
  }

  /**
   * Registers an event listener
   *
   * @param {string} event - Event name
   * @param {Function} callback - Event callback function
   * @returns {MariaDBStore} This instance for chaining
   */
  on(event, callback) {
    this._emitter.on(event, callback);
    return this;
  }

  /**
   * Registers a one-time event listener
   *
   * @param {string} event - Event name
   * @param {Function} callback - Event callback function
   * @returns {MariaDBStore} This instance for chaining
   */
  once(event, callback) {
    this._emitter.once(event, callback);
    return this;
  }

  /**
   * Creates the sessions table if it doesn't exist
   *
   * @async
   * @returns {Promise<void>}
   * @throws {Error} If table creation fails
   */
  async createTable() {
    try {
      const conn = await this.pool.getConnection();
      try {
        await conn.query(`
          CREATE TABLE IF NOT EXISTS \`${this.options.table}\` (
            \`session_id\` VARCHAR(128) NOT NULL,
            \`expires\` INT UNSIGNED NOT NULL,
            \`data\` MEDIUMTEXT,
            PRIMARY KEY (\`session_id\`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin
        `);
        console.debug(`Sessions table '${this.options.table}' created or already exists`);
      } finally {
        conn.release();
      }
    } catch (err) {
      console.error('Error creating sessions table:', err);
      this._emitter.emit('error', err);
      throw err;
    }
  }

  /**
   * Starts the periodic cleanup of expired sessions
   */
  startCleanup() {
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.cleanup();
      } catch (err) {
        console.error('Error during session cleanup:', err);
      }
    }, this.options.clearInterval * 1000);

    this.cleanupInterval.unref();
  }

  /**
   * Removes expired sessions from the database
   *
   * @async
   * @returns {Promise<void>}
   * @throws {Error} If cleanup operation fails
   */
  async cleanup() {
    try {
      const conn = await this.pool.getConnection();
      try {
        const now = Math.floor(Date.now() / 1000);
        const [result] = await conn.query(
          `DELETE FROM \`${this.options.table}\` WHERE \`expires\` < ?`,
          [now]
        );

        if (result.affectedRows > 0) {
          console.debug(`Cleaned up ${result.affectedRows} expired sessions`);
        }
      } finally {
        conn.release();
      }
    } catch (err) {
      console.error('Error cleaning up expired sessions:', err);
      this._emitter.emit('error', err);
      throw err;
    }
  }

  /**
   * Gets a session from the store
   *
   * @async
   * @param {string} sid - Session ID
   * @param {Function} callback - Callback function(error, session)
   * @returns {Promise<void>}
   */
  async get(sid, callback) {
    try {
      const conn = await this.pool.getConnection();
      try {
        const now = Math.floor(Date.now() / 1000);
        const [rows] = await conn.query(
          `SELECT \`data\` FROM \`${this.options.table}\` WHERE \`session_id\` = ? AND \`expires\` > ?`,
          [sid, now]
        );

        if (rows.length === 0) {
          return callback(null, null);
        }

        let sessionData;
        try {
          sessionData = JSON.parse(rows[0].data);
        } catch (err) {
          return callback(err);
        }

        return callback(null, sessionData);
      } finally {
        conn.release();
      }
    } catch (err) {
      console.error('Error getting session:', err);
      this._emitter.emit('error', err);
      return callback(err);
    }
  }

  /**
   * Saves a session to the store
   *
   * @async
   * @param {string} sid - Session ID
   * @param {Object} session - Session data
   * @param {Function} callback - Callback function(error)
   * @returns {Promise<void>}
   */
  async set(sid, session, callback) {
    try {
      const conn = await this.pool.getConnection();
      try {
        const expires = Math.floor((session.cookie.expires instanceof Date
          ? session.cookie.expires.getTime()
          : Date.now() + this.options.ttl * 1000) / 1000);

        const data = JSON.stringify(session);

        await conn.query(
          `INSERT INTO \`${this.options.table}\` (\`session_id\`, \`expires\`, \`data\`)
           VALUES (?, ?, ?)
           ON DUPLICATE KEY UPDATE \`expires\` = ?, \`data\` = ?`,
          [sid, expires, data, expires, data]
        );

        return callback(null);
      } finally {
        conn.release();
      }
    } catch (err) {
      console.error('Error setting session:', err);
      this._emitter.emit('error', err);
      return callback(err);
    }
  }

  /**
   * Removes a session from the store
   *
   * @async
   * @param {string} sid - Session ID
   * @param {Function} callback - Callback function(error)
   * @returns {Promise<void>}
   */
  async destroy(sid, callback) {
    try {
      const conn = await this.pool.getConnection();
      try {
        await conn.query(
          `DELETE FROM \`${this.options.table}\` WHERE \`session_id\` = ?`,
          [sid]
        );

        return callback(null);
      } finally {
        conn.release();
      }
    } catch (err) {
      console.error('Error destroying session:', err);
      this._emitter.emit('error', err);
      return callback(err);
    }
  }

  /**
   * Gets all active sessions from the store
   *
   * @async
   * @param {Function} callback - Callback function(error, sessions)
   * @returns {Promise<void>}
   */
  async all(callback) {
    try {
      const conn = await this.pool.getConnection();
      try {
        const now = Math.floor(Date.now() / 1000);
        const [rows] = await conn.query(
          `SELECT \`session_id\`, \`data\` FROM \`${this.options.table}\` WHERE \`expires\` > ?`,
          [now]
        );

        const sessions = {};
        rows.forEach(row => {
          try {
            sessions[row.session_id] = JSON.parse(row.data);
          } catch (err) {
            console.warn(`Error parsing session data for ${row.session_id}:`, err);
          }
        });

        return callback(null, sessions);
      } finally {
        conn.release();
      }
    } catch (err) {
      console.error('Error getting all sessions:', err);
      this._emitter.emit('error', err);
      return callback(err);
    }
  }

  /**
   * Gets the count of active sessions
   *
   * @async
   * @param {Function} callback - Callback function(error, count)
   * @returns {Promise<void>}
   */
  async length(callback) {
    try {
      const conn = await this.pool.getConnection();
      try {
        const now = Math.floor(Date.now() / 1000);
        const [rows] = await conn.query(
          `SELECT COUNT(*) AS count FROM \`${this.options.table}\` WHERE \`expires\` > ?`,
          [now]
        );

        return callback(null, rows[0].count);
      } finally {
        conn.release();
      }
    } catch (err) {
      console.error('Error getting session count:', err);
      this._emitter.emit('error', err);
      return callback(err);
    }
  }

  /**
   * Removes all sessions from the store
   *
   * @async
   * @param {Function} callback - Callback function(error)
   * @returns {Promise<void>}
   */
  async clear(callback) {
    try {
      const conn = await this.pool.getConnection();
      try {
        await conn.query(`TRUNCATE TABLE \`${this.options.table}\``);
        return callback(null);
      } finally {
        conn.release();
      }
    } catch (err) {
      console.error('Error clearing sessions:', err);
      this._emitter.emit('error', err);
      return callback(err);
    }
  }

  /**
   * Updates the expiration time of a session
   *
   * @async
   * @param {string} sid - Session ID
   * @param {Object} session - Session data
   * @param {Function} callback - Callback function(error)
   * @returns {Promise<void>}
   */
  async touch(sid, session, callback) {
    try {
      const conn = await this.pool.getConnection();
      try {
        const expires = Math.floor((session.cookie.expires instanceof Date
          ? session.cookie.expires.getTime()
          : Date.now() + this.options.ttl * 1000) / 1000);

        await conn.query(
          `UPDATE \`${this.options.table}\` SET \`expires\` = ? WHERE \`session_id\` = ?`,
          [expires, sid]
        );

        return callback(null);
      } finally {
        conn.release();
      }
    } catch (err) {
      console.error('Error touching session:', err);
      this._emitter.emit('error', err);
      return callback(err);
    }
  }

  /**
   * Closes the store and releases all resources
   *
   * @async
   * @returns {Promise<void>}
   */
  async close() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    if (this.pool) {
      await this.pool.end();
      this.pool = null;
    }

    console.debug('MariaDB session store closed');
  }
}

export default MariaDBStore;
