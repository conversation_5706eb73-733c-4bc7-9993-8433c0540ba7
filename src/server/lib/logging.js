import log4js from 'log4js';

const projectName = '[cm-echo]';

log4js.configure({
  appenders: {
    [projectName]: {
      type: 'dateFile',
      filename: `./logs/${process.env.PROJECT_NAME}.log`,
      numBackups: 30,
      compress: true,
      maxLogSize: 10485760,
      keepFileExt: true,
      mode: 0o664,
    },
    console: { type: 'console' },
  },
  categories: {
    default: {
      appenders: [projectName, 'console'],
      level: 'trace',
    },
  },
});

const console = log4js.getLogger(projectName);

export default console;
