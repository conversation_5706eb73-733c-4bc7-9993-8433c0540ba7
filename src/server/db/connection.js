import mariadb from 'mariadb';
import console from '../lib/logging.js';

const pool = mariadb.createPool({
  host: process.env.MARIADB_HOST,
  database: process.env.MARIADB_NAME,
  user: process.env.MARIADB_USER,
  password: process.env.MARIADB_PASSWORD,
  connectionLimit: 5,
  supportBigNumbers: true,
  bigNumberStrings: true,
  typeCast: true,
  timezone: 'Z'
});

/**
 * Tests the database connection by attempting to get a connection from the pool.
 *
 * @returns {Promise<boolean>} True if connection is successful, false otherwise
 */
async function testConnection() {
  let conn;
  try {
    conn = await pool.getConnection();
    console.debug('Database connection successful');
    return true;
  } catch (err) {
    console.error('Database connection error:', err);
    return false;
  } finally {
    if (conn) conn.release();
  }
}

/**
 * Initializes the database by creating necessary tables if they don't exist.
 * Creates the process_sessions table and ensures required columns exist.
 *
 * @returns {Promise<boolean>} True if initialization is successful, false otherwise
 */
async function initDatabase() {
  let conn;
  try {
    conn = await pool.getConnection();

    await conn.query(`
      CREATE TABLE IF NOT EXISTS process_sessions (
        id VARCHAR(32) PRIMARY KEY,
        request_id VARCHAR(32) NOT NULL,
        data LONGTEXT NOT NULL,
        events LONGTEXT NOT NULL,
        status VARCHAR(20) NOT NULL,
        total INT NOT NULL,
        processed INT NOT NULL,
        cancelled BOOLEAN DEFAULT FALSE,
        user_id INT,
        username VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    try {
      await conn.query(`
        ALTER TABLE process_sessions ADD COLUMN IF NOT EXISTS user_id INT
      `);
      await conn.query(`
        ALTER TABLE process_sessions ADD COLUMN IF NOT EXISTS username VARCHAR(255)
      `);
    } catch (err) {
      console.error('Error adding user columns to process_sessions table:', err);
    }

    console.debug('Database initialized successfully');
    return true;
  } catch (err) {
    console.error('Database initialization error:', err);
    return false;
  } finally {
    if (conn) conn.release();
  }
}

export { pool, testConnection, initDatabase };
