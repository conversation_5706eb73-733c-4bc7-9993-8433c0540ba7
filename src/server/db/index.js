import { pool, testConnection, initDatabase } from './connection.js';
import { createSession, getSession, getAllSessions, getIncompleteSessions, updateSession, addSessionEvent, deleteSession, cleanupOldSessions, getActiveSessionsForUser } from './process-sessions.js';
import { createUsersTable, createPasswordResetTokensTable, createAccountLockoutTable, createUser, findUserByUsername, findUserById, findUserByEmail, verifyPassword, updateUserPassword, changeUserPassword, createPasswordResetToken, verifyPasswordResetToken, deletePasswordResetToken, getAllUsers, isUserLocked, incrementFailedLogins, resetFailedLoginAttempts, updateUserLockStatus } from './users.js';
import console from '../lib/logging.js';

/**
 * Initializes the database module by testing connection, creating tables, and setting up required structures.
 * This function should be called when the application starts to ensure the database is properly set up.
 *
 * @returns {Promise<boolean>} True if all initialization steps are successful, false otherwise
 */
async function initDb() {
  try {
    const connectionSuccess = await testConnection();
    if (!connectionSuccess) {
      console.error('Failed to connect to database. Check your database configuration.');
      return false;
    }

    const initSuccess = await initDatabase();
    if (!initSuccess) {
      console.error('Failed to initialize database. Check database permissions.');
      return false;
    }

    const usersTableSuccess = await createUsersTable();
    if (!usersTableSuccess) {
      console.error('Failed to create users table. Check database permissions.');
      return false;
    }

    const passwordResetTokensTableSuccess = await createPasswordResetTokensTable();
    if (!passwordResetTokensTableSuccess) {
      console.error('Failed to create password reset tokens table. Check database permissions.');
      return false;
    }

    const accountLockoutTableSuccess = await createAccountLockoutTable();
    if (!accountLockoutTableSuccess) {
      console.error('Failed to create account lockout table. Check database permissions.');
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error initializing database module:', error);
    return false;
  }
}

export {
  pool,
  initDb,
  createSession,
  getSession,
  getAllSessions,
  getIncompleteSessions,
  updateSession,
  addSessionEvent,
  deleteSession,
  cleanupOldSessions,
  getActiveSessionsForUser,
  createUser,
  findUserByUsername,
  findUserById,
  findUserByEmail,
  verifyPassword,
  updateUserPassword,
  changeUserPassword,
  getAllUsers,
  createPasswordResetToken,
  verifyPasswordResetToken,
  deletePasswordResetToken,
  isUserLocked,
  incrementFailedLogins,
  resetFailedLoginAttempts,
  updateUserLockStatus
};
