import { pool } from './connection.js';
import console from '../lib/logging.js';

/**
 * Creates a new process session in the database.
 *
 * @param {Object} session - The session object to create
 * @param {string} session.id - Unique identifier for the session
 * @param {string} session.requestId - Request identifier for logging
 * @param {Object} session.data - Data associated with the session (MSISDNs, subscriptionIds, etc.)
 * @param {Array} session.events - Array of events associated with the session
 * @param {string} session.status - Current status of the session
 * @param {number} session.total - Total number of items to process
 * @param {number} session.processed - Number of items processed so far
 * @param {boolean} session.cancelled - Whether the session has been cancelled
 * @param {boolean} session.paused - Whether the session is paused
 * @param {number} session.userId - ID of the user who created the session
 * @param {string} session.username - Username of the user who created the session
 * @returns {Promise<boolean>} True if session was created successfully, false otherwise
 */
async function createSession(session) {
  let conn;
  try {
    conn = await pool.getConnection();

    const { id, requestId, data, events, status, total, processed, cancelled, paused, userId, username } = session;

    const safeData = data || {};
    const safeEvents = Array.isArray(events) ? events : [];

    try {
      await conn.query(`
        ALTER TABLE process_sessions ADD COLUMN IF NOT EXISTS paused BOOLEAN DEFAULT FALSE
      `);
    } catch (err) {
      console.error(`[${session.requestId}]`, 'Error adding paused column to process_sessions table:', err);
    }

    await conn.query(`
      INSERT INTO process_sessions (id, request_id, data, events, status, total, processed, cancelled, paused, user_id, username)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      id,
      requestId,
      JSON.stringify(safeData),
      JSON.stringify(safeEvents),
      status,
      total,
      processed,
      cancelled,
      paused || false,
      userId || null,
      username || null
    ]);

    return true;
  } catch (err) {
    console.error(`[${session.requestId}]`, 'Error creating session in database:', err);
    return false;
  } finally {
    if (conn) conn.release();
  }
}

/**
 * Retrieves a session from the database by its ID.
 * Parses JSON data and events, and sets up the session object for use.
 *
 * @param {string} sessionId - The ID of the session to retrieve
 * @returns {Promise<Object|null>} The session object if found, null otherwise
 */
async function getSession(sessionId) {
  let conn;
  try {
    conn = await pool.getConnection();

    const rows = await conn.query(`
      SELECT * FROM process_sessions WHERE id = ?
    `, [sessionId]);

    if (rows.length === 0) {
      return null;
    }

    const session = rows[0];

    try {
      if (session.data) {
        if (typeof session.data === 'object' && !Array.isArray(session.data)) {
        }
        else if (typeof session.data === 'string') {
          if (session.data.trim() !== '') {
            session.data = JSON.parse(session.data);
          } else {
            session.data = {};
          }
        }
        else if (Array.isArray(session.data)) {
          session.data = JSON.parse(JSON.stringify(session.data));
        } else {
          session.data = {};
        }
      } else {
        session.data = {};
      }

      if (typeof session.data !== 'object' || Array.isArray(session.data)) {
        session.data = {};
      }
    } catch (dataError) {
      console.warn(`Failed to parse data for session ${sessionId}, initializing empty object:`, dataError);
      session.data = {};
    }

    try {
      if (session.events) {
        if (Array.isArray(session.events)) {
        }
        else if (typeof session.events === 'string') {
          if (session.events.trim() !== '') {
            session.events = JSON.parse(session.events);
          } else {
            session.events = [];
          }
        }
        else if (typeof session.events === 'object') {
          session.events = JSON.parse(JSON.stringify(session.events));
        } else {
          session.events = [];
        }
      } else {
        session.events = [];
      }

      if (!Array.isArray(session.events)) {
        session.events = [];
      }

      session.events = session.events.map(event => {
        if (!event.timestamp) {
          event.timestamp = new Date().toISOString();
        }

        if (typeof event.total !== 'number') {
          event.total = session.total || 0;
        }

        if (typeof event.processed !== 'number' && event.status !== 'error') {
          event.processed = event.processed || session.processed || 0;
        }

        return event;
      });

      session.events.sort((a, b) => {
        if (a.timestamp && b.timestamp) {
          return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
        }
        return (a.processed || 0) - (b.processed || 0);
      });

      console.debug(`Processed ${session.events.length} events for session ${sessionId}`);
    } catch (eventsError) {
      console.warn(`Failed to parse events for session ${sessionId}, initializing empty array:`, eventsError);
      session.events = [];
    }

    session.clients = new Set();

    session.requestId = session.request_id;

    return session;
  } catch (err) {
    console.error('Error getting session from database:', err);
    return null;
  } finally {
    if (conn) conn.release();
  }
}

/**
 * Updates an existing session in the database.
 * Handles conversion of JavaScript object properties to database field names.
 *
 * @param {string} sessionId - The ID of the session to update
 * @param {Object} updates - Object containing the fields to update
 * @returns {Promise<boolean>} True if update was successful, false otherwise
 */
async function updateSession(sessionId, updates) {
  let conn;
  try {
    conn = await pool.getConnection();

    try {
      await conn.query(`
        ALTER TABLE process_sessions ADD COLUMN IF NOT EXISTS paused BOOLEAN DEFAULT FALSE
      `);
    } catch (err) {
      console.error('Error adding paused column to process_sessions table:', err);
    }

    const updateFields = [];
    const updateValues = [];

    Object.entries(updates).forEach(([key, value]) => {
      const dbField = key.replace(/([A-Z])/g, '_$1').toLowerCase();

      if (key === 'data') {
        updateFields.push(`${dbField} = ?`);
        const safeData = value || {};
        updateValues.push(JSON.stringify(safeData));
      } else if (key === 'events') {
        updateFields.push(`${dbField} = ?`);
        const safeEvents = Array.isArray(value) ? value : [];
        updateValues.push(JSON.stringify(safeEvents));
      } else {
        updateFields.push(`${dbField} = ?`);
        updateValues.push(value);
      }
    });

    updateValues.push(sessionId);

    await conn.query(`
      UPDATE process_sessions
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `, updateValues);

    return true;
  } catch (err) {
    console.error('Error updating session in database:', err);
    return false;
  } finally {
    if (conn) conn.release();
  }
}

/**
 * Adds a new event to a session's event log.
 * Ensures the event has a timestamp and avoids adding duplicate events.
 *
 * @param {string} sessionId - The ID of the session to add the event to
 * @param {Object} event - The event object to add
 * @param {string} event.status - Status of the event
 * @param {string} [event.message] - Message associated with the event
 * @param {number} [event.processed] - Number of items processed at this event
 * @param {string} [event.timestamp] - Timestamp of the event (added if not provided)
 * @returns {Promise<boolean>} True if event was added successfully, false otherwise
 */
async function addSessionEvent(sessionId, event) {
  let conn;
  try {
    conn = await pool.getConnection();

    if (!event.timestamp) {
      event.timestamp = new Date().toISOString();
    }

    console.debug(`Adding event to session ${sessionId}:`, JSON.stringify(event));

    const rows = await conn.query(`
      SELECT events FROM process_sessions WHERE id = ?
    `, [sessionId]);

    if (rows.length === 0) {
      console.warn(`Session ${sessionId} not found when adding event`);
      return false;
    }

    let events = [];
    try {
      if (rows[0].events) {
        if (Array.isArray(rows[0].events)) {
          events = rows[0].events;
        }
        else if (typeof rows[0].events === 'string') {
          if (rows[0].events.trim() !== '') {
            events = JSON.parse(rows[0].events);
          }
        }
        else if (typeof rows[0].events === 'object') {
          events = JSON.parse(JSON.stringify(rows[0].events));
        }
      }
    } catch (parseError) {
      console.warn(`Failed to parse events for session ${sessionId}, initializing empty array:`, parseError);
      events = [];
    }

    if (!Array.isArray(events)) {
      events = [];
    }

    const isDuplicate = events.some(e =>
      e.status === event.status &&
      e.message === event.message &&
      e.processed === event.processed
    );

    if (isDuplicate) {
      console.debug(`Skipping duplicate event for session ${sessionId}:`, JSON.stringify(event));
      return true;
    }

    events.push(event);

    await conn.query(`
      UPDATE process_sessions
      SET events = ?
      WHERE id = ?
    `, [JSON.stringify(events), sessionId]);

    console.debug(`Successfully added event to session ${sessionId}, total events: ${events.length}`);
    return true;
  } catch (err) {
    console.error('Error adding event to session in database:', err);
    return false;
  } finally {
    if (conn) conn.release();
  }
}

/**
 * Deletes a session from the database.
 *
 * @param {string} sessionId - The ID of the session to delete
 * @returns {Promise<boolean>} True if deletion was successful, false otherwise
 */
async function deleteSession(sessionId) {
  let conn;
  try {
    conn = await pool.getConnection();

    await conn.query(`
      DELETE FROM process_sessions WHERE id = ?
    `, [sessionId]);

    return true;
  } catch (err) {
    console.error('Error deleting session from database:', err);
    return false;
  } finally {
    if (conn) conn.release();
  }
}

/**
 * Retrieves all sessions from the database.
 * Parses JSON data and events for each session.
 *
 * @returns {Promise<Array>} Array of session objects, empty array if none found or error occurs
 */
async function getAllSessions() {
  let conn;
  try {
    conn = await pool.getConnection();

    const rows = await conn.query(`
      SELECT * FROM process_sessions
      ORDER BY created_at DESC
    `);

    if (rows.length === 0) {
      return [];
    }

    const sessions = [];
    for (const row of rows) {
      const session = { ...row };

      try {
        if (session.data) {
          if (typeof session.data === 'object' && !Array.isArray(session.data)) {
          } else if (typeof session.data === 'string') {
            if (session.data.trim() !== '') {
              session.data = JSON.parse(session.data);
            } else {
            session.data = {};
          }
        } else if (Array.isArray(session.data)) {
          session.data = JSON.parse(JSON.stringify(session.data));
        } else {
            session.data = {};
          }
        } else {
        session.data = {};
      }

      if (typeof session.data !== 'object' || Array.isArray(session.data)) {
        session.data = {};
        }
      } catch (dataError) {
        console.warn(`Failed to parse data for session ${session.id}, initializing empty object:`, dataError);
        session.data = {};
      }

      try {
        if (session.events) {
          if (Array.isArray(session.events)) {
          } else if (typeof session.events === 'string') {
            if (session.events.trim() !== '') {
              session.events = JSON.parse(session.events);
            } else {
            session.events = [];
          }
        } else if (typeof session.events === 'object') {
          session.events = JSON.parse(JSON.stringify(session.events));
        } else {
            session.events = [];
          }
        } else {
        session.events = [];
      }

      if (!Array.isArray(session.events)) {
        session.events = [];
        }
      } catch (eventsError) {
        console.warn(`Failed to parse events for session ${session.id}, initializing empty array:`, eventsError);
        session.events = [];
      }

      session.clients = new Set();

      session.requestId = session.request_id;

      sessions.push(session);
    }

    return sessions;
  } catch (err) {
    console.error('Error getting all sessions from database:', err);
    return [];
  } finally {
    if (conn) conn.release();
  }
}

/**
 * Retrieves all incomplete (processing and not cancelled) sessions from the database.
 * Used for recovery of sessions that were interrupted.
 *
 * @returns {Promise<Array>} Array of incomplete session objects, empty array if none found or error occurs
 */
async function getIncompleteSessions() {
  let conn;
  try {
    conn = await pool.getConnection();

    const rows = await conn.query(`
      SELECT * FROM process_sessions
      WHERE status = 'processing' AND cancelled = FALSE
      ORDER BY created_at ASC
    `);

    if (rows.length === 0) {
      return [];
    }

    const sessions = [];
    for (const row of rows) {
      const session = { ...row };

      try {
        if (session.data) {
          if (typeof session.data === 'object' && !Array.isArray(session.data)) {
          } else if (typeof session.data === 'string') {
            if (session.data.trim() !== '') {
              session.data = JSON.parse(session.data);
            } else {
            session.data = {};
          }
        } else if (Array.isArray(session.data)) {
          session.data = JSON.parse(JSON.stringify(session.data));
        } else {
            session.data = {};
          }
        } else {
        session.data = {};
      }

      if (typeof session.data !== 'object' || Array.isArray(session.data)) {
        session.data = {};
        }
      } catch (dataError) {
        console.warn(`Failed to parse data for session ${session.id}, initializing empty object:`, dataError);
        session.data = {};
      }

      try {
        if (session.events) {
          if (Array.isArray(session.events)) {
          } else if (typeof session.events === 'string') {
            if (session.events.trim() !== '') {
              session.events = JSON.parse(session.events);
            } else {
            session.events = [];
          }
        } else if (typeof session.events === 'object') {
          session.events = JSON.parse(JSON.stringify(session.events));
        } else {
            session.events = [];
          }
        } else {
        session.events = [];
      }

      if (!Array.isArray(session.events)) {
        session.events = [];
        }
      } catch (eventsError) {
        console.warn(`Failed to parse events for session ${session.id}, initializing empty array:`, eventsError);
        session.events = [];
      }

      session.clients = new Set();

      session.requestId = session.request_id;

      sessions.push(session);
    }

    return sessions;
  } catch (err) {
    console.error('Error getting incomplete sessions from database:', err);
    return [];
  } finally {
    if (conn) conn.release();
  }
}

/**
 * Removes old sessions from the database based on their age.
 *
 * @param {number} maxAgeHours - Maximum age in hours for sessions to keep (default: 1)
 * @returns {Promise<number>} Number of sessions deleted
 */
async function cleanupOldSessions(maxAgeHours = 1) {
  let conn;
  try {
    conn = await pool.getConnection();

    const result = await conn.query(`
      DELETE FROM process_sessions
      WHERE updated_at < DATE_SUB(NOW(), INTERVAL ? HOUR)
    `, [maxAgeHours]);

    const deletedCount = result.affectedRows;
    if (deletedCount > 0) {
      console.debug(`Cleaned up ${deletedCount} old sessions from database`);
    }

    return deletedCount;
  } catch (err) {
    console.error('Error cleaning up old sessions from database:', err);
    return 0;
  } finally {
    if (conn) conn.release();
  }
}

/**
 * Retrieves all active sessions for a specific user.
 * Active sessions are those not in completed, failed, error, or cancelled status.
 *
 * @param {number} userId - The ID of the user to get sessions for
 * @returns {Promise<Array>} Array of active session objects for the user
 */
async function getActiveSessionsForUser(userId) {
  let conn;
  try {
    conn = await pool.getConnection();

    const rows = await conn.query(`
      SELECT * FROM process_sessions
      WHERE user_id = ?
      AND status NOT IN ('completed', 'failed', 'error', 'cancelled')
      ORDER BY created_at DESC
    `, [userId]);

    return rows.map(row => {
      let data = {};
      let events = [];

      try {
        if (row.data && typeof row.data === 'string') {
          data = JSON.parse(row.data);
        } else if (row.data) {
          data = row.data;
        }
      } catch (err) {
        console.warn(`Failed to parse data for session ${row.id}:`, err);
      }

      try {
        if (row.events && typeof row.events === 'string') {
          events = JSON.parse(row.events);
        } else if (row.events) {
          events = row.events;
        }
      } catch (err) {
        console.warn(`Failed to parse events for session ${row.id}:`, err);
      }

      return {
        id: row.id,
        requestId: row.request_id,
        data,
        events,
        status: row.status,
        total: row.total,
        processed: row.processed,
        cancelled: row.cancelled === 1,
        paused: row.paused === 1,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        userId: row.user_id,
        username: row.username
      };
    });
  } catch (err) {
    console.error('Error getting active sessions for user:', err);
    return [];
  } finally {
    if (conn) conn.release();
  }
}

export {
  createSession,
  getSession,
  getAllSessions,
  getIncompleteSessions,
  updateSession,
  addSessionEvent,
  deleteSession,
  cleanupOldSessions,
  getActiveSessionsForUser
};
