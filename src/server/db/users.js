import {pool} from './connection.js';
import bcrypt from 'bcrypt';
import crypto from 'crypto';
import console from '../lib/logging.js';

const SALT_ROUNDS = 10;
const MAX_FAILED_ATTEMPTS = 5;

/**
 * Creates the "users" table in the database if it doesn't exist.
 *
 * @returns {Promise<boolean>} True if table was created or already exists, false on error
 */
async function createUsersTable() {
  let conn;
  try {
    conn = await pool.getConnection();

    await conn.query(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    console.debug('Users table created or already exists');
    return true;
  } catch (err) {
    console.error('Error creating users table:', err);
    return false;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Creates the account_lockout table in the database if it doesn't exist.
 * This table tracks failed login attempts and account lockout status.
 *
 * @returns {Promise<boolean>} True if table was created or already exists, false on error
 */
async function createAccountLockoutTable() {
  let conn;
  try {
    conn = await pool.getConnection();

    await conn.query(`
      CREATE TABLE IF NOT EXISTS account_lockout (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(255) NOT NULL UNIQUE,
        failed_attempts INT DEFAULT 0,
        is_locked BOOLEAN DEFAULT FALSE,
        locked_until TIMESTAMP NULL,
        last_failed_attempt TIMESTAMP NULL,
        FOREIGN KEY (username) REFERENCES users(username) ON DELETE CASCADE
      )
    `);

    console.debug('Account lockout table created or already exists');
    return true;
  } catch (err) {
    console.error('Error creating account lockout table:', err);
    return false;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Creates a new user in the database.
 * Hashes the password before storing it.
 *
 * @param {Object} user - User object with user details
 * @param {string} user.username - Username for the new user
 * @param {string} user.password - Plain text password (will be hashed)
 * @param {string} [user.email] - Email address for the user
 * @returns {Promise<Object|null>} Created a user object without password if successful, null otherwise
 */
async function createUser(user) {
  let conn;
  try {
    conn = await pool.getConnection();

    const hashedPassword = await bcrypt.hash(user.password, SALT_ROUNDS);

    const result = await conn.query(`
      INSERT INTO users (username, password, email)
      VALUES (?, ?, ?)
    `, [
      user.username,
      hashedPassword,
      user.email || null
    ]);

    if (result.affectedRows === 1) {
      const users = await conn.query(`
        SELECT id, username, email, created_at, updated_at
        FROM users
        WHERE id = ?
      `, [result.insertId]);

      return users[0];
    }

    return null;
  } catch (err) {
    console.error('Error creating user:', err);
    return null;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Finds a user by their username.
 *
 * @param {string} username - Username is to search for
 * @returns {Promise<Object|null>} User object if found, null otherwise
 */
async function findUserByUsername(username) {
  let conn;
  try {
    conn = await pool.getConnection();

    const users = await conn.query(`
      SELECT *
      FROM users
      WHERE username = ?
    `, [username]);

    return users.length > 0 ? users[0] : null;
  } catch (err) {
    console.error('Error finding user by username:', err);
    return null;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Finds a user by their ID.
 * Returns a limited set of user data (excludes password).
 *
 * @param {number} id - User ID to search for
 * @returns {Promise<Object|null>} User object without a password if found, null otherwise
 */
async function findUserById(id) {
  let conn;
  try {
    conn = await pool.getConnection();

    const users = await conn.query(`
      SELECT id, username, email, created_at, updated_at
      FROM users
      WHERE id = ?
    `, [id]);

    return users.length > 0 ? users[0] : null;
  } catch (err) {
    console.error('Error finding user by ID:', err);
    return null;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Verifies a password against a stored hash.
 *
 * @param {string} providedPassword - Plain text password to verify
 * @param {string} storedHash - Hashed password from the database
 * @returns {Promise<boolean>} True if password matches, false otherwise
 */
async function verifyPassword(providedPassword, storedHash) {
  try {
    return await bcrypt.compare(providedPassword, storedHash);
  } catch (err) {
    console.error('Error verifying password:', err);
    return false;
  }
}

/**
 * Finds a user by their email address.
 *
 * @param {string} email - Email address to search for
 * @returns {Promise<Object|null>} User object if found, null otherwise
 */
async function findUserByEmail(email) {
  let conn;
  try {
    conn = await pool.getConnection();

    const users = await conn.query(`
      SELECT *
      FROM users
      WHERE email = ?
    `, [email]);

    return users.length > 0 ? users[0] : null;
  } catch (err) {
    console.error('Error finding user by email:', err);
    return null;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Updates a user's password.
 * Hashes the new password before storing it.
 *
 * @param {number} userId - ID of the user to update
 * @param {string} newPassword - New plain text password (will be hashed)
 * @returns {Promise<boolean>} True if password was updated successfully, false otherwise
 */
async function updateUserPassword(userId, newPassword) {
  let conn;
  try {
    conn = await pool.getConnection();

    const hashedPassword = await bcrypt.hash(newPassword, SALT_ROUNDS);

    const result = await conn.query(`
      UPDATE users
      SET password = ?
      WHERE id = ?
    `, [
      hashedPassword,
      userId
    ]);

    return result.affectedRows === 1;
  } catch (err) {
    console.error('Error updating user password:', err);
    return false;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Changes a user's password after verifying the current password.
 * Used for user-initiated password changes.
 *
 * @param {number} userId - ID of the user changing their password
 * @param {string} currentPassword - Current plain text password for verification
 * @param {string} newPassword - New plain text password to set
 * @returns {Promise<boolean>} True if password was changed successfully, false otherwise
 */
async function changeUserPassword(userId, currentPassword, newPassword) {
  let conn;
  try {
    conn = await pool.getConnection();

    const users = await conn.query(`
      SELECT *
      FROM users
      WHERE id = ?
    `, [userId]);

    if (users.length === 0) {
      return false;
    }

    const user = users[0];

    const isValid = await verifyPassword(currentPassword, user.password);
    if (!isValid) {
      return false;
    }

    const isSamePassword = await verifyPassword(newPassword, user.password);
    if (isSamePassword) {
      return 'same-password';
    }

    return await updateUserPassword(userId, newPassword);
  } catch (err) {
    console.error('Error changing user password:', err);
    return false;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Creates the password_reset_tokens table in the database if it doesn't exist.
 * This table stores tokens for password reset functionality.
 *
 * @returns {Promise<boolean>} True if table was created or already exists, false on error
 */
async function createPasswordResetTokensTable() {
  let conn;
  try {
    conn = await pool.getConnection();

    await conn.query(`
      CREATE TABLE IF NOT EXISTS password_reset_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    console.debug('Password reset tokens table created or already exists');
    return true;
  } catch (err) {
    console.error('Error creating password reset tokens table:', err);
    return false;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Creates a password reset token for a user.
 * Deletes any existing tokens for the user first.
 *
 * @param {number} userId - ID of the user requesting password reset
 * @returns {Promise<Object|null>} Object with token details if successful, null otherwise
 */
async function createPasswordResetToken(userId) {
  let conn;
  try {
    conn = await pool.getConnection();

    const token = crypto.randomBytes(32).toString('hex');

    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 1);

    await conn.query(`
      DELETE FROM password_reset_tokens
      WHERE user_id = ?
    `, [userId]);

    const result = await conn.query(`
      INSERT INTO password_reset_tokens (user_id, token, expires_at)
      VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 1 HOUR))
    `, [
      userId,
      token
    ]);

    const tokenData = await conn.query(`
      SELECT expires_at FROM password_reset_tokens
      WHERE user_id = ? AND token = ?
    `, [userId, token]);

    if (tokenData.length > 0) {
      console.debug('Actual expiration time in DB:', tokenData[0].expires_at);
    }

    if (result.affectedRows === 1) {
      const dbExpiresAt = tokenData.length > 0 ? tokenData[0].expires_at : expiresAt;

      return {
        userId,
        token,
        expiresAt: dbExpiresAt
      };
    }

    return null;
  } catch (err) {
    console.error('Error creating password reset token:', err);
    return null;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Verifies a password reset token.
 * Checks if the token exists and has not expired.
 *
 * @param {string} token - The token to verify
 * @returns {Promise<Object|null>} Object with userId if token is valid, null otherwise
 */
async function verifyPasswordResetToken(token) {
  let conn;
  try {
    conn = await pool.getConnection();

    const tokens = await conn.query(`
      SELECT * FROM password_reset_tokens
      WHERE token = ? AND expires_at > NOW()
    `, [token]);

    if (tokens.length === 0) {
      return null;
    }

    return { userId: tokens[0].user_id };
  } catch (err) {
    console.error('Error verifying password reset token:', err);
    return null;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Deletes a password reset token.
 * Used after a successful password reset.
 *
 * @param {string} token - The token to delete
 * @returns {Promise<boolean>} True if token was deleted, false otherwise
 */
async function deletePasswordResetToken(token) {
  let conn;
  try {
    conn = await pool.getConnection();

    const result = await conn.query(`
      DELETE FROM password_reset_tokens
      WHERE token = ?
    `, [token]);

    return result.affectedRows > 0;
  } catch (err) {
    console.error('Error deleting password reset token:', err);
    return false;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Retrieves all users from the database.
 * Returns limited user data (excludes passwords).
 *
 * @returns {Promise<Array>} Array of user objects, empty array if none found or error occurs
 */
async function getAllUsers() {
  let conn;
  try {
    conn = await pool.getConnection();

    return await conn.query(`
      SELECT id, username, email, created_at, updated_at
      FROM users
      ORDER BY username ASC
    `);
  } catch (err) {
    console.error('Error getting all users:', err);
    return [];
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Checks if a user account is locked.
 *
 * @param {string} username - Username to check
 * @returns {Promise<boolean>} True if an account is locked, false otherwise
 */
async function isUserLocked(username) {
  let conn;
  try {
    conn = await pool.getConnection();

    // First, check if the user exists
    const userExists = await conn.query(`
      SELECT 1 FROM users WHERE username = ?
    `, [username]);

    if (userExists.length === 0) {
      return false; // User doesn't exist, so we don't consider it locked
    }

    // Make sure there's an entry in the lockout table
    await conn.query(`
      INSERT IGNORE INTO account_lockout (username, failed_attempts, is_locked)
      VALUES (?, 0, FALSE)
    `, [username]);

    // Then check if an account is locked and hasn't expired
    const lockInfo = await conn.query(`
      SELECT is_locked, locked_until
      FROM account_lockout
      WHERE username = ?
    `, [username]);

    if (lockInfo.length === 0) {
      return false;
    }

    const { is_locked, locked_until } = lockInfo[0];

    // If an account is locked but the lock time has expired, unlock it
    if (is_locked && locked_until && new Date() > new Date(locked_until)) {
      await conn.query(`
        UPDATE account_lockout
        SET is_locked = FALSE, failed_attempts = 0, locked_until = NULL
        WHERE username = ?
      `, [username]);
      return false;
    }

    return is_locked;
  } catch (err) {
    console.error('Error checking if user is locked:', err);
    return false; // Default to not lock in case of error
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Increments the failed login attempts for a user.
 * Locks the account if the maximum number of attempts is reached.
 *
 * @param {string} username - Username to increment failed attempts for
 * @returns {Promise<Object>} Object with lockout information
 */
async function incrementFailedLogins(username) {
  let conn;
  try {
    conn = await pool.getConnection();

    const userExists = await conn.query(`
      SELECT 1 FROM users WHERE username = ?
    `, [username]);

    if (userExists.length === 0) {
      return { lockoutThreshold: 1, isLocked: false };
    }

    await conn.query(`
      INSERT IGNORE INTO account_lockout (username, failed_attempts, is_locked)
      VALUES (?, 0, FALSE)
    `, [username]);

    const lastFailedAttempt = new Date();

    await conn.query(`
      UPDATE account_lockout
      SET failed_attempts = failed_attempts + 1,
          last_failed_attempt = ?
      WHERE username = ?
    `, [lastFailedAttempt, username]);

    const attemptsResult = await conn.query(`
      SELECT failed_attempts
      FROM account_lockout
      WHERE username = ?
    `, [username]);

    if (attemptsResult.length === 0) {
      return { lockoutThreshold: 1, isLocked: false };
    }

    const failedAttempts = attemptsResult[0].failed_attempts;

    if (failedAttempts >= MAX_FAILED_ATTEMPTS) {
      const lockedUntil = new Date();
      lockedUntil.setMinutes(lockedUntil.getMinutes() + 30);

      await conn.query(`
        UPDATE account_lockout
        SET is_locked = TRUE, locked_until = ?
        WHERE username = ?
      `, [lockedUntil, username]);

      return { lockoutThreshold: failedAttempts, isLocked: true };
    }

    return { lockoutThreshold: failedAttempts, isLocked: false };
  } catch (err) {
    console.error('Error incrementing failed logins:', err);
    return { lockoutThreshold: 0, isLocked: false };
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Resets the failed login attempts for a user.
 * Used after a successful login.
 *
 * @param {string} username - Username to reset failed attempts for
 * @returns {Promise<boolean>} True if reset was successful, false otherwise
 */
async function resetFailedLoginAttempts(username) {
  let conn;
  try {
    conn = await pool.getConnection();

    await conn.query(`
      INSERT IGNORE INTO account_lockout (username, failed_attempts, is_locked)
      VALUES (?, 0, FALSE)
    `, [username]);

    const result = await conn.query(`
      UPDATE account_lockout
      SET failed_attempts = 0, is_locked = FALSE, locked_until = NULL
      WHERE username = ?
    `, [username]);

    return result.affectedRows > 0;
  } catch (err) {
    console.error('Error resetting failed login attempts:', err);
    return false;
  } finally {
    if (conn) await conn.release();
  }
}

/**
 * Manually updates the lock status for a user account.
 * Can be used by administrators to lock or unlock accounts.
 *
 * @param {string} username - Username to update lock status for
 * @param {boolean} isLocked - Whether to lock (true) or unlock (false) the account
 * @returns {Promise<boolean>} True if update was successful, false otherwise
 */
async function updateUserLockStatus(username, isLocked) {
  let conn;
  try {
    conn = await pool.getConnection();

    const userExists = await conn.query(`
      SELECT 1 FROM users WHERE username = ?
    `, [username]);

    if (userExists.length === 0) {
      return false;
    }

    await conn.query(`
      INSERT IGNORE INTO account_lockout (username, failed_attempts, is_locked)
      VALUES (?, 0, ?)
    `, [username, isLocked]);

    const result = await conn.query(`
      UPDATE account_lockout
      SET is_locked = ?, 
          failed_attempts = IF(? = FALSE, 0, failed_attempts),
          locked_until = IF(? = TRUE, DATE_ADD(NOW(), INTERVAL 30 MINUTE), NULL)
      WHERE username = ?
    `, [isLocked, isLocked, isLocked, username]);

    return result.affectedRows > 0;
  } catch (err) {
    console.error('Error updating user lock status:', err);
    return false;
  } finally {
    if (conn) await conn.release();
  }
}

export {
  createUsersTable,
  createPasswordResetTokensTable,
  createAccountLockoutTable,
  createUser,
  findUserByUsername,
  findUserById,
  findUserByEmail,
  verifyPassword,
  updateUserPassword,
  changeUserPassword,
  createPasswordResetToken,
  verifyPasswordResetToken,
  deletePasswordResetToken,
  getAllUsers,
  isUserLocked,
  incrementFailedLogins,
  resetFailedLoginAttempts,
  updateUserLockStatus
};
