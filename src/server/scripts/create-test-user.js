import { initDb, createUser } from '../db/index.js';
import console from '../lib/logging.js';

// Test user credentials
const testUser = {
  username: 'admin',
  password: 'password',
  email: '<EMAIL>'
};

async function createTestUser() {
  try {
    // Initialize the database
    const dbInitialized = await initDb();
    if (!dbInitialized) {
      console.error('Failed to initialize database. Cannot create test user.');
      process.exit(1);
    }

    // Check if user already exists
    console.info(`Creating test user: ${testUser.username}`);
    const user = await createUser(testUser);

    if (user) {
      console.info('Test user created successfully:', {
        id: user.id,
        username: user.username,
        email: user.email
      });
    } else {
      console.error('Failed to create test user. User might already exist.');
    }
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    process.exit(0);
  }
}

// Run the function
createTestUser();
