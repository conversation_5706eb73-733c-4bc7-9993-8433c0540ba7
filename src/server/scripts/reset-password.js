import { initDb, findUserByUsername, findUserByEmail, updateUserPassword } from '../db/index.js';
import sendEmail from '../services/email.js';
import console from '../lib/logging.js';
import readline from 'readline';
import crypto from 'crypto';

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to generate a random password
function generateRandomPassword(length = 10) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_';
  let password = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, chars.length);
    password += chars.charAt(randomIndex);
  }

  return password;
}

// Function to validate email format
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// These functions are now imported from '../db/index.js'

// Main function to reset a user's password
async function resetUserPassword() {
  try {
    // Initialize the database
    const dbInitialized = await initDb();
    if (!dbInitialized) {
      console.error('Failed to initialize database. Cannot reset password.');
      process.exit(1);
    }

    // Prompt for email or username
    rl.question('Enter email or username: ', async (input) => {
      let user;

      // Check if input is an email or username
      if (isValidEmail(input)) {
        // Find user by email
        user = await findUserByEmail(input);
      } else {
        // Find user by username
        user = await findUserByUsername(input);
      }

      // Check if user exists
      if (!user) {
        console.error('User not found. Please check the email or username and try again.');
        rl.close();
        process.exit(1);
      }

      // Generate new random password
      const newPassword = generateRandomPassword();

      // Update user's password
      const updated = await updateUserPassword(user.id, newPassword);

      if (updated) {
        console.info(`Password reset successful for user: ${user.username}`);

        // Send email with new password if user has an email
        if (user.email) {
          const emailSubject = 'CM Echo Account';
          const emailBody = `
            <h2>Скидання паролю в системі CM Echo</h2>
            <p>Ваш пароль був успішно скинутий.</p>
            <p><strong>Ім'я користувача:</strong> ${user.username}</p>
            <p><strong>Новий пароль:</strong> ${newPassword}</p>
            <p>Будь ласка, збережіть цю інформацію в безпеці та обов'язково змініть пароль після входу.</p>
          `;


          const emailSent = await sendEmail(user.email, emailSubject, emailBody);

          if (emailSent) {
            console.info(`Email with new password sent to ${user.email}`);
          } else {
            console.error(`Failed to send email to ${user.email}. Please provide the new password manually.`);
            console.info('New password:', newPassword);
          }
        } else {
          console.warn('User does not have an email address. Please provide the new password manually.');
          console.info('New password:', newPassword);
        }
      } else {
        console.error('Failed to reset password.');
      }

      rl.close();
      process.exit(0);
    });
  } catch (error) {
    console.error('Error resetting password:', error);
    rl.close();
    process.exit(1);
  }
}

// Run the function
resetUserPassword();
