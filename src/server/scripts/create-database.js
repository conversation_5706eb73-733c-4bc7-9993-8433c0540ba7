import mariadb from 'mariadb';
import console from '../lib/logging.js';

// Create a connection without specifying a database
const pool = mariadb.createPool({
  host: process.env.MARIADB_HOST,
  port: process.env.MARIADB_PORT || 3306,
  user: process.env.MARIADB_USER,
  password: process.env.MARIADB_PASSWORD,
  connectionLimit: 5,
  timezone: 'Z'
});

async function createDatabase() {
  let conn;
  try {
    conn = await pool.getConnection();

    // Get database name from environment variable
    const dbName = process.env.MARIADB_NAME || 'cm-echo';

    console.info(`Checking if database '${dbName}' exists...`);

    // Check if database exists
    const databases = await conn.query('SHOW DATABASES');
    const dbExists = databases.some(db => db.Database === dbName);

    if (dbExists) {
      console.info(`Database '${dbName}' already exists.`);
    } else {
      // Create database
      console.info(`Creating database '${dbName}'...`);
      await conn.query(`CREATE DATABASE IF NOT EXISTS \`${dbName}\``);
      console.info(`Database '${dbName}' created successfully.`);
    }

    return true;
  } catch (err) {
    console.error('Error creating database:', err);
    return false;
  } finally {
    if (conn) conn.release();
    // Close the pool
    await pool.end();
  }
}

// Run the function
createDatabase()
  .then(success => {
    if (success) {
      console.info('Database setup completed successfully.');
    } else {
      console.error('Database setup failed.');
    }
    process.exit(0);
  })
  .catch(err => {
    console.error('Unexpected error during database setup:', err);
    process.exit(1);
  });
