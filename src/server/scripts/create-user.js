import { initDb, createUser, findUserByUsername } from '../db/index.js';
import sendEmail from '../services/email.js';
import console from '../lib/logging.js';
import readline from 'readline';
import crypto from 'crypto';

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to generate a random password
function generateRandomPassword(length = 10) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_';
  let password = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, chars.length);
    password += chars.charAt(randomIndex);
  }

  return password;
}

// Function to extract username from email (part before @)
function extractUsernameFromEmail(email) {
  return email.split('@')[0];
}

// Function to validate email format
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Main function to create a new user
async function createNewUser() {
  try {
    // Initialize the database
    const dbInitialized = await initDb();
    if (!dbInitialized) {
      console.error('Failed to initialize database. Cannot create user.');
      process.exit(1);
    }

    // Prompt for email
    rl.question('Enter email address: ', async (email) => {
      // Validate email format
      if (!isValidEmail(email)) {
        console.error('Invalid email format. Please provide a valid email address.');
        rl.close();
        process.exit(1);
      }

      // Extract username from email
      const username = extractUsernameFromEmail(email);

      // Check if user already exists
      const existingUser = await findUserByUsername(username);
      if (existingUser) {
        console.error(`User with username '${username}' already exists.`);
        rl.close();
        process.exit(1);
      }

      // Generate random password
      const password = generateRandomPassword();

      // Create user object
      const newUser = {
        username,
        password,
        email
      };

      console.info(`Creating new user: ${username} (${email})`);

      // Create user in database
      const user = await createUser(newUser);

      if (user) {
        console.info('User created successfully:', {
          id: user.id,
          username: user.username,
          email: user.email
        });

        // Send email with credentials
        const emailSubject = 'CM Echo Account';
        const emailBody = `
        <h2>Система CM Echo</h2>
        <p>Ваш обліковий запис успішно створено.</p>
        <p><strong>Ім'я користувача:</strong> ${username}</p>
        <p><strong>Пароль:</strong> ${password}</p>
        <p>Будь ласка, збережіть цю інформацію в безпеці та змініть пароль після першого входу.</p>
        `;

        const emailSent = await sendEmail(email, emailSubject, emailBody);

        if (emailSent) {
          console.info(`Email with credentials sent to ${email}`);
        } else {
          console.error(`Failed to send email to ${email}. Please provide the credentials manually.`);
          console.info('Credentials:', { username, password });
        }
      } else {
        console.error('Failed to create user.');
      }

      rl.close();
      process.exit(0);
    });
  } catch (error) {
    console.error('Error creating user:', error);
    rl.close();
    process.exit(1);
  }
}

// Run the function
createNewUser();
