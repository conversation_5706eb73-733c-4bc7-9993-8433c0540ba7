# Stage 1: Build frontend and docs
FROM node:20 as builder

WORKDIR /home/<USER>/app
COPY  package*.json ./

RUN npm install

COPY . .
RUN npm run build

# Stage 2: Install only production dependencies
FROM node:20 as deps

WORKDIR /home/<USER>/app

COPY package*.json ./
RUN npm install --omit=dev

# Stage 3: Final image
FROM node:20-slim
EXPOSE 8080

# Set timezone configuration
ENV TZ=Europe/Kiev

WORKDIR /app

COPY --from=deps /home/<USER>/app/node_modules ./node_modules
COPY --from=builder /home/<USER>/app/package.json ./
COPY --from=builder /home/<USER>/app/src/server ./src/server
COPY --from=builder /home/<USER>/app/src/frontend/dist ./src/frontend/dist
COPY --from=builder /home/<USER>/app/src/docs/.vitepress/dist ./src/docs/.vitepress/dist

RUN chown -R node: /app
RUN chmod 0755 /app
USER node

CMD ["npm", "start"]

HEALTHCHECK --interval=20s --timeout=3s \
  CMD ps aux | grep -E 'node|app' | grep -v grep || exit 1